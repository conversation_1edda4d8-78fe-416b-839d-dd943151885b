import { test, Page, expect } from '@playwright/test';

import {
  STAY_BASE_URL,
  createOperatorWithLocationGroup,
  getStayTheme,
  commonGuestInfo,
  getReservationId,
  getCustomConciergeContent,
  connectionConfigurations,
  generateRoomNumber,
  HotelReservationStatusEnum,
  getCheckoutTimeForClock,
} from '../../../utils';
import {
  createPosSystem,
  patchLocationTheme,
  updateReservationMockResponse,
  updateProfileMockResponse,
  addBillingInstructionOptions,
  patchPaymentLocationGroupInternal,
  patchStaySettings,
  startWebSocketConnections,
  deletePosSystem,
} from '../../../network';
import {
  defaultBillingInstructions,
  getPosCreationPayload,
} from '../../../constants';

connectionConfigurations.forEach(({ name, useWebSocket }) => {
  test.describe.parallel(`Concierge check-in flow (${name})`, () => {
    let testOperator;
    let testAxiosInstance;
    let testGroup;
    let testLocation;
    let posCreationPayload;
    let posSystemId;

    test.beforeAll(async () => {
      const { axiosInstance, operator, baseLocation, locationGroup } =
        await createOperatorWithLocationGroup({
          useAdyen: true,
        });
      testOperator = operator;
      testAxiosInstance = axiosInstance;
      testLocation = baseLocation;
      testGroup = locationGroup;

      posCreationPayload = await getPosCreationPayload({ useWebSocket });
      await setupTestEnvironment();
    });

    test.afterAll(async () => {
      if (useWebSocket) {
        await deletePosSystem({
          groupId: testGroup.groupId,
          systemId: posSystemId,
          axiosInstance: testAxiosInstance,
        });
      }

      testOperator = null;
      testAxiosInstance = null;
      testLocation = null;
      testGroup = null;
      posCreationPayload = null;
      posSystemId = null;
    });

    const setupTestEnvironment = async () => {
      // set theme
      await patchLocationTheme({
        axiosInstance: testAxiosInstance,
        locationId: testLocation.locationId,
        theme: getStayTheme(),
      });

      await addBillingInstructionOptions({
        axiosInstance: testAxiosInstance,
        groupId: testGroup.groupId,
        billingInstructions: defaultBillingInstructions,
      });

      // connect to hospitality
      const createPosSystemResponse = await createPosSystem({
        axiosInstance: testAxiosInstance,
        groupId: testGroup.groupId,
        payload: posCreationPayload,
      });

      posSystemId = createPosSystemResponse.data.systemId;

      if (useWebSocket) {
        await startWebSocketConnections({
          axiosInstance: testAxiosInstance,
        });
      }

      // Setup theme
      await patchPaymentLocationGroupInternal({
        groupId: testGroup.groupId,
        updateValues: {
          meldescheinEnabled: true,
          useRealTimeUpdates: useWebSocket,
        },
      });
    };

    test.describe(`with settings: `, () => {
      const scenarios = [
        // Check-out is possible
        {
          description:
            'Checkout day, TotalOpenAmount > 0 -> Check-out is not possible right now, pay action first',
          isCheckoutPossible: true, // But pay first
          isChargesPosted: true,
          nights: '0',
        },
        {
          description:
            'Checkout day, TotalOpenAmount = 0, earliest checkout time is not set -> Check-out is possible',
          isCheckoutPossible: true,
          earliestCheckoutTime: null,
          nights: '0',
        },
        {
          description:
            'Checkout day, Earliest checkout time > current, balance = 0 -> Check-out is possible',
          isCheckoutPossible: true,
          earliestCheckoutTime: 8,
          nights: '0',
        },
        {
          description:
            'Checkout day, Earliest checkout time > current, balance > 0 -> Check-out is possible',
          isCheckoutPossible: true,
          earliestCheckoutTime: 8,
          isChargesPosted: true,
          nights: '0',
        },
        // Check-out is not possible
        {
          description:
            'Not a checkout day, earliest checkout time is not set, open amount = 0 -> Check-out is not possible',
          isCheckoutPossible: false,
        },
        {
          description:
            'Not a checkout day, earliest checkout time is not set, open amount > 0 -> Check-out is not possible',
          isCheckoutPossible: false,
          isChargesPosted: true,
        },
        {
          description:
            'Checkout day, earliest checkout time is set and its too early, balance = 0 -> Check-out is not possible',
          isCheckoutPossible: false,
          earliestCheckoutTime: 8,
          nights: '0',
        },
        {
          description:
            'Checkout day, earliest checkout time is set and its too early, balance > 0 -> Check-out is not possible',
          isCheckoutPossible: false,
          earliestCheckoutTime: 8,
          isChargesPosted: true,
          nights: '0',
        },
        // TODO: Same as last two, but day is wrong and time is right after earliest checkout time ?
        {
          description:
            'Consumer check-out is disabled -> Check-out is not possible',
          isCheckoutPossible: false,
          consumerCheckoutEnabled: false,
          nights: '0',
        },
        {
          description:
            'Day before checkout,TotalOpenAmount > 0, but consumerChargesPaymentsEnabled:false -> Payment is not possible -> Check-out is not possible',
          isCheckoutPossible: false,
          consumerChargesPaymentsEnabled: false,
          isChargesPosted: true,
        },
        {
          description:
            'Checkout day, TotalOpenAmount > 0, but consumerChargesPaymentsEnabled:false -> Payment is not possible -> Check-out is not possible',
          isCheckoutPossible: false,
          consumerChargesPaymentsEnabled: false,
          isChargesPosted: true,
          nights: '0',
        },
      ];

      scenarios.forEach(
        ({
          description,
          isCheckoutPossible,
          consumerCheckoutEnabled,
          isChargesPosted,
          earliestCheckoutTime,
          nights,
          consumerChargesPaymentsEnabled,
        }) => {
          test(`${description}`, async ({ page }: { page: Page }) => {
            // Patch stay settings according to test case
            await patchStaySettings({
              axiosInstance: testAxiosInstance,
              groupId: testGroup.groupId,
              updateValues: {
                postChargesEnabled: false,
                consumerCheckoutEnabled: consumerCheckoutEnabled ?? true,
                consumerChargesPaymentsEnabled:
                  consumerChargesPaymentsEnabled ?? true,
                earliestCheckoutTime,
                customConciergeContent: getCustomConciergeContent(),
              },
            });

            // Get reservationId
            const reservationId = getReservationId();
            const roomId = generateRoomNumber();
            const openAmount = isChargesPosted ? '200' : '0';

            // Update mock server to get expected type of body for specific test
            await updateReservationMockResponse({
              axiosInstance: testAxiosInstance,
              query: {
                hotelId: posCreationPayload.credentials.hotelId,
                roomId,
                useWebSocket: useWebSocket ? 'true' : 'false',
                reservationStatus: HotelReservationStatusEnum.IN_HOUSE,
                isChargesPosted: isChargesPosted ? 'true' : 'false', // If we need a balance > 0
                nights,
              },
              reservationId: reservationId,
            });
            await updateProfileMockResponse({
              axiosInstance: testAxiosInstance,
              query: {
                reservationId: reservationId,
              },
            });

            // Set browser time for earliestCheckoutTime tests
            if (earliestCheckoutTime) {
              const addMinutes = isCheckoutPossible ? 30 : -30; // 30 minutes after or before checkout time
              const checkoutTime = getCheckoutTimeForClock(
                earliestCheckoutTime,
                addMinutes
              );
              await page.clock.setFixedTime(checkoutTime);
            }

            const url = `${STAY_BASE_URL}/${testGroup.discoverId}?isTesting=true`;

            await page.goto(url);

            // Login
            await page
              .getByTestId('conciergeLoginPmsReference')
              .fill(reservationId);
            await page
              .getByTestId('conciergeLoginLastName')
              .fill(commonGuestInfo.surname);
            if (useWebSocket) {
              await page.waitForTimeout(500);
            }
            await page.getByTestId('conciergeLoginSubmit').click();

            // Click on reservations sidebar entry to start with check-in
            await page.getByTestId('conciergeSideBarReservations').click();

            // Open amount is right
            await expect(page.getByTestId('openAmountValue')).toContainText(
              openAmount
            );

            if (isCheckoutPossible) {
              // Check-out description
              await expect(
                page.getByTestId('checkOutDescription')
              ).toBeVisible();
              // Click on checkout button
              await page.getByTestId('checkOutButton').click();
              // Headline
              await expect(page.getByTestId('checkoutHeadline')).toBeVisible();
              // Total open amount
              await expect(
                page.getByTestId('openAmountTotalValue')
              ).toContainText(openAmount);

              if (isChargesPosted) {
                // Private bill
                await expect(
                  page.getByTestId('privateBillLabel')
                ).toBeVisible();
                // Open amount
                await expect(page.getByTestId('openAmountValue')).toContainText(
                  openAmount
                );
              } else {
                // Empty bill
                await expect(page.getByTestId('emptyBillLabel')).toBeVisible();
              }
              // Continue button
              await expect(page.getByTestId('continueButton')).toBeVisible();
            } else {
              if (
                !nights &&
                isChargesPosted &&
                !(consumerChargesPaymentsEnabled == false)
              ) {
                await expect(
                  page.getByTestId('paymentDescription')
                ).toBeVisible();
                await page.getByTestId('paymentButton').click();
                // Headline
                await expect(
                  page.getByTestId('paymentOverviewHeadline')
                ).toBeVisible();
                // Description
                await expect(
                  page.getByTestId('paymentOverviewDescription')
                ).toBeVisible();
              }
              if (earliestCheckoutTime) {
                await expect(
                  page.getByTestId('checkOutBeforeEarliestCheckoutDescription')
                ).toBeVisible();
              }
              if (!nights && !isChargesPosted) {
                await expect(
                  page.getByTestId('paymentNoOpenAmountDescription')
                ).toBeVisible();
              }
              if (consumerCheckoutEnabled == false) {
                await expect(page.getByTestId('checkOutButton')).toBeHidden();
                await expect(
                  page.getByTestId('checkOutDescription')
                ).toBeHidden();
                await expect(page.getByTestId('paymentButton')).toBeHidden();
                await expect(
                  page.getByTestId('paymentDescription')
                ).toBeHidden();
                await expect(
                  page.getByTestId('checkOutBeforeEarliestCheckoutDescription')
                ).toBeHidden();
              }
              if (consumerChargesPaymentsEnabled == false) {
                if (nights) {
                  // I will fail here after LUCA-29882, just delete me and leave only else case
                  await page.getByTestId('checkOutButton').click();
                } else {
                  await expect(
                    page.getByTestId('paymentNotEnabledDescription')
                  ).toBeVisible();
                }
              }
            }
          });
        }
      );
    });
  });
});
