---
description:
globs:
alwaysApply: false
---
# Hub Service Guide

## Overview

The hub service uses a modern Next.js stack that differs significantly from
other Luca services:

- **Next.js 15** with App Router and React 19
- **Tailwind CSS v4** for styling (NOT styled-components)
- **shadcn/ui** components based on Radix UI primitives (NOT antd)
- **Lucide React** for icons
- **TanStack Query** for data fetching and caching
- **React Intl** for internationalization
- **Next Themes** for theme management
- **Zod** for validation
- **AI SDK** for AI features
- **class-variance-authority (cva)** for component variants

## Next.js App Router Patterns

### File Structure and Routing

- Use **App Router** with `app/` directory structure
- Page components: `page.tsx` in route directories
- Layout components: `layout.tsx` for shared layouts
- Use **route groups** with parentheses: `(logged-in)/`, `(public)/`
- API routes in `app/api/` directory
- Use **Server Components** by default, mark with `'use client'` only when
  needed

### Component Organization

```
src/
├── app/                    # Next.js App Router pages and layouts
├── components/
│   ├── ui/                # shadcn/ui components (reusable UI primitives)
│   ├── [feature]/         # Feature-specific components
│   └── providers/         # Context providers
├── lib/                   # Utility functions and configurations
├── hooks/                 # Custom React hooks
├── utils/                 # Utility functions
├── types/                 # TypeScript type definitions
├── constants/             # Constant values
└── messages/              # Internationalization messages
```

## TypeScript and Component Patterns

### Component Definition

- Use **interfaces** instead of types for component props (different from other
  services)
- Use descriptive prop interface names (NOT PropertiesType):

```tsx
interface ButtonProps extends React.ComponentProps<'button'> {
  variant?:
    | 'default'
    | 'destructive'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
}

export function Button({
  variant = 'default',
  size = 'default',
  ...props
}: ButtonProps) {
  // implementation
}
```

### File Naming Conventions

- Components: `component-name.tsx` (kebab-case, NOT PascalCase)
- Pages: `page.tsx`, `layout.tsx`, `loading.tsx`, `error.tsx`
- No `.react.tsx` extension (different from other services)
- No separate `.styled.ts` files (use Tailwind classes)

## Styling with Tailwind CSS

### Tailwind Patterns

- Use **utility-first** approach with Tailwind classes
- Use `cn()` utility for conditional classes:

```tsx
import { cn } from '@/lib/utils';

<div
  className={cn('base-classes', condition && 'conditional-classes', className)}
/>;
```

### Component Variants with CVA

- Use **class-variance-authority** for component variants:

```tsx
import { cva, type VariantProps } from 'class-variance-authority';

const buttonVariants = cva(
  'base-classes', // base styles
  {
    variants: {
      variant: {
        default: 'variant-specific-classes',
        destructive: 'destructive-classes',
      },
      size: {
        default: 'default-size-classes',
        sm: 'small-size-classes',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);
```

### Theme Integration

- Use **CSS custom properties** for theming
- Leverage **next-themes** for dark/light mode
- Use semantic color tokens: `bg-background`, `text-foreground`, `border-border`

## shadcn/ui Component Patterns

### Component Structure

- Extend **Radix UI primitives** for accessibility
- Use **forwardRef** when needed for ref passing
- Use **Slot** from Radix for polymorphic components:

```tsx
import { Slot } from '@radix-ui/react-slot';

const Comp = asChild ? Slot : 'button';
return <Comp {...props} />;
```

### UI Component Guidelines

- Keep UI components in `components/ui/` directory
- Export both component and variant functions
- Use consistent naming: `ComponentName` and `componentVariants`
- Add proper TypeScript types with `VariantProps`

## State Management Patterns

### Data Fetching

- Use **TanStack Query** for server state management
- Implement proper loading states and error handling
- Use React Suspense for loading UI

### Global State

- Use **React Context** with providers for global state
- Organize providers in `components/providers/`
- Create provider composition in root `providers.tsx`

### Client vs Server Components

- Default to **Server Components** for better performance
- Use `'use client'` directive only when necessary:
  - Event handlers
  - Browser-only APIs
  - useState, useEffect hooks
  - Interactive components

## Import and Export Patterns

### Absolute Imports

- Use **@/** aliases configured in `components.json`:

```tsx
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { api } from '@/lib/api';
```

### Barrel Exports

- Use index files for clean imports
- Export components with named exports

## Form and Validation

### Form Handling

- Use **Zod** for schema validation
- Implement proper form error handling
- Use controlled components with proper TypeScript typing

## Performance Optimization

### Next.js Specific

- Use **dynamic imports** for code splitting
- Optimize images with `next/image`
- Implement proper caching strategies
- Use **Suspense** for streaming

### React Patterns

- Use **React.memo** for expensive re-renders
- Implement proper key props for lists
- Use **useCallback** and **useMemo** judiciously

## Development Guidelines

### Code Style

- Use **arrow functions** for component definitions
- Use **const assertions** for better type inference
- Keep components focused and single-responsibility
- Use descriptive variable names

### Error Handling

- Implement **error boundaries** for error catching
- Use proper error states in components
- Handle loading and error states consistently

### Accessibility

- Leverage **Radix UI** accessibility features
- Use proper ARIA attributes
- Implement keyboard navigation
- Test with screen readers

## AI Integration Patterns

### AI SDK Usage

- Use **@ai-sdk/react** for AI-powered features
- Implement streaming responses properly
- Handle AI loading states and errors

## Differences from Other Luca Services

**Key Differences to Remember:**

1. **NO styled-components** - use Tailwind CSS
2. **NO antd components** - use shadcn/ui components
3. **NO PropertiesType** - use descriptive interface names
4. **NO .react.tsx** extensions - use .tsx
5. **NO unstated-next** - use React Context and TanStack Query
6. **Use interfaces** instead of types for component props
7. **File naming**: kebab-case for components, not PascalCase
8. **Next.js patterns**: App Router, Server Components, route groups

## Common Patterns to Follow

```tsx
// ✅ Correct hub service pattern
interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

export function Dialog({ open, onOpenChange, children }: DialogProps) {
  return (
    <DialogPrimitive.Root open={open} onOpenChange={onOpenChange}>
      <DialogPrimitive.Content
        className={cn(
          'fixed left-[50%] top-[50%] z-50 translate-x-[-50%] translate-y-[-50%]',
          'grid w-full max-w-lg gap-4 border bg-background p-6 shadow-lg',
          'duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out'
        )}
      >
        {children}
      </DialogPrimitive.Content>
    </DialogPrimitive.Root>
  );
}
```

This hub service follows modern React and Next.js best practices with a focus on
performance, accessibility, and developer experience.
