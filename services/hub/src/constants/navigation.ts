import { Home, Bot, LucideIcon } from 'lucide-react';

export interface NavigationItem {
  title: string;
  url: string;
  icon: LucideIcon;
}

export interface NavigationGroup {
  title: string;
  items: NavigationItem[];
}

// Navigation items grouped by category
export const navigationItems: NavigationGroup[] = [
  {
    title: 'Main',
    items: [
      {
        title: 'Dashboard',
        url: '/dashboard',
        icon: Home,
      },
    ],
  },
  {
    title: 'Features',
    items: [
      {
        title: 'AI Assistant',
        url: '/ai-assistant',
        icon: Bo<PERSON>,
      },
    ],
  },
];
