// Role mappings based on support portal constants
export const ROLE_MAPPINGS = {
  'prj-luca-A': 'Admin',
  'prj-luca-M': 'Marketing',
  'prj-luca-S': 'Sales',
  'prj-luca-SUP': 'Support',
  'prj-luca-SUPLINK': 'Link Support',
  'prj-luca-CSM': 'Customer Success Manager',
  'prj-luca-D': 'Developer',
  'default-roles-nexenio': 'Default User',
  'offline_access': 'Offline Access',
  'uma_authorization': 'Authorization',
} as const;

export function mapRolesToFriendlyNames(roles?: string[]): string[] {
  if (!roles || roles.length === 0) {
    return [];
  }

  return roles
    .map(role => ROLE_MAPPINGS[role as keyof typeof ROLE_MAPPINGS] || role)
    .filter(Boolean);
}

export function getPrimaryRole(roles?: string[]): string {
  if (!roles || roles.length === 0) {
    return 'User';
  }

  // Priority order for displaying primary role
  const rolePriority = [
    'prj-luca-A', // Admin
    'prj-luca-D', // Developer
    'prj-luca-CSM', // CSM
    'prj-luca-SUP', // Support
    'prj-luca-SUPLINK', // Link Support
    'prj-luca-S', // Sales
    'prj-luca-M', // Marketing
  ];

  for (const priorityRole of rolePriority) {
    if (roles.includes(priorityRole)) {
      return ROLE_MAPPINGS[priorityRole as keyof typeof ROLE_MAPPINGS];
    }
  }

  // If no priority role found, return the first mapped role or 'User'
  const mappedRoles = mapRolesToFriendlyNames(roles);
  return mappedRoles[0] || 'User';
}
