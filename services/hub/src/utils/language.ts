export enum SupportedLanguageEnum {
  de = 'de',
  en = 'en',
}

export const defaultLanguage = SupportedLanguageEnum.en;

const isSupportedLanguage = (lang: string): lang is SupportedLanguageEnum =>
  lang === SupportedLanguageEnum.de || lang === SupportedLanguageEnum.en;

export const getLanguage = (): SupportedLanguageEnum => {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    return defaultLanguage;
  }

  const language = navigator.language.split(/[_-]/)[0];
  if (isSupportedLanguage(language)) {
    return language;
  }

  return defaultLanguage;
};

export const getBrowserOrDefaultLanguage = (): SupportedLanguageEnum => {
  return getLanguage();
};
