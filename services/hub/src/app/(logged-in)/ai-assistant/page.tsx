'use client';

import { useIntl } from 'react-intl';
import { ChatInterface } from '@/components/ai/chat-interface';

export default function AIAssistantPage() {
  const intl = useIntl();

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-medium">
          {intl.formatMessage({ id: 'ai.assistant.title' })}
        </h1>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'ai.assistant.description' })}
        </p>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 min-h-0">
        <ChatInterface />
      </div>
    </div>
  );
}
