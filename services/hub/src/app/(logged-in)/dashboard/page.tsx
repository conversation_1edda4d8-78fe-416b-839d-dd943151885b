'use client';

import { useIntl } from 'react-intl';

export default function Dashboard() {
  const intl = useIntl();

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-3xl font-medium">
          {intl.formatMessage({ id: 'dashboard.welcome' })}
        </h1>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'app.description' })}
        </p>
      </div>
    </div>
  );
}
