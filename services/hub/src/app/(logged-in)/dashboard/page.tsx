'use client';

import { useIntl } from 'react-intl';
import { AnalyticsOverview } from '../../../components/dashboard/analytics-overview';
import { RecentActivity } from '../../../components/dashboard/recent-activity';
import { KnowledgeSearch } from '../../../components/knowledge/knowledge-search';

export default function Dashboard() {
  const intl = useIntl();

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div>
        <h1 className="text-3xl font-medium">
          {intl.formatMessage({ id: 'dashboard.welcome' })}
        </h1>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'app.description' })}
        </p>
      </div>

      {/* Analytics Overview */}
      <AnalyticsOverview />

      {/* Main Content Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Activity */}
        <RecentActivity />

        {/* Knowledge Search */}
        <KnowledgeSearch />
      </div>
    </div>
  );
}
