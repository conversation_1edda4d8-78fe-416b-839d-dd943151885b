'use client';

import { useIntl } from 'react-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useGetMe } from '@/hooks/use-get-me';
import { mapRolesToFriendlyNames, getPrimaryRole } from '@/utils/roles';

export default function Profile() {
  const intl = useIntl();
  const { user } = useGetMe();

  const primaryRole = getPrimaryRole(user?.roles);
  const allRoles = mapRolesToFriendlyNames(user?.roles);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-medium">
          {intl.formatMessage({ id: 'profile.title' })}
        </h1>
        <p className="text-muted-foreground">
          {intl.formatMessage({ id: 'profile.description' })}
        </p>
      </div>

      {/* User Information Card */}
      <Card>
        <CardHeader>
          <CardTitle>
            {intl.formatMessage({ id: 'profile.userInfo' })}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="flex justify-between items-center py-2 border-b">
              <span className="font-medium">
                {intl.formatMessage({ id: 'profile.email' })}:
              </span>
              <span>{user?.email}</span>
            </div>
            {user?.name && (
              <div className="flex justify-between items-center py-2 border-b">
                <span className="font-medium">
                  {intl.formatMessage({ id: 'profile.name' })}:
                </span>
                <span>{user.name}</span>
              </div>
            )}
            <div className="flex justify-between items-center py-2 border-b">
              <span className="font-medium">
                {intl.formatMessage({ id: 'profile.primaryRole' })}:
              </span>
              <span>{primaryRole}</span>
            </div>
            {allRoles && allRoles.length > 0 && (
              <div className="flex justify-between items-start py-2">
                <span className="font-medium">
                  {intl.formatMessage({ id: 'profile.allRoles' })}:
                </span>
                <div className="text-right">
                  {allRoles.map((role, index) => (
                    <div key={index} className="text-sm">
                      {role}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
