import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    const result = streamText({
      model: openai('gpt-4o-mini'),
      system: `You are a helpful AI assistant for luca, an internal company tool.

You help luca team members with:
- General questions about luca services and products
- Technical support and troubleshooting
- Information about company processes and workflows
- Data analysis and insights
- Task automation and productivity tips

Be professional, helpful, and concise in your responses. If you don't know something specific about luca's internal systems, be honest about it and suggest who they might contact for more detailed information.`,
      messages,
      maxTokens: 1000,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
