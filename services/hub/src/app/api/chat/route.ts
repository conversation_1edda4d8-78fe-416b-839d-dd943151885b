import { streamText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { appRouter } from '../../../server/routers/_app';
import { createTRPCContext } from '../../../lib/trpc';

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    // Create tRPC context for function calls
    const trpcContext = await createTRPCContext({ req });
    const caller = appRouter.createCaller(trpcContext);

    const result = streamText({
      model: openai('gpt-4o-mini'),
      system: `You are a helpful AI assistant for luca, an internal company tool.

You help luca team members with:
- General questions about luca services and products
- Technical support and troubleshooting
- Information about company processes and workflows
- Data analysis and insights from the luca database
- Task automation and productivity tips
- Knowledge base search and management

You have access to the following tools:
- Search users, locations, and analytics data
- Search the knowledge base for company information
- Get system health and statistics
- Proxy complex AI requests to backend-python service

Be professional, helpful, and concise in your responses. Use the available tools to provide accurate, data-driven answers when possible.`,
      messages,
      maxTokens: 1500,
      tools: {
        searchUsers: tool({
          description: 'Search for users in the luca system',
          parameters: z.object({
            query: z.string().describe('Search query for users'),
            limit: z
              .number()
              .optional()
              .describe('Number of results to return (default: 10)'),
          }),
          execute: async ({ query, limit = 10 }) => {
            const results = await caller.users.search({ query, limit });
            return { results, count: results.length };
          },
        }),

        searchLocations: tool({
          description: 'Search for locations in the luca system',
          parameters: z.object({
            query: z.string().describe('Search query for locations'),
            limit: z
              .number()
              .optional()
              .describe('Number of results to return (default: 10)'),
          }),
          execute: async ({ query, limit = 10 }) => {
            const results = await caller.locations.search({ query, limit });
            return { results, count: results.length };
          },
        }),

        getAnalytics: tool({
          description: 'Get analytics and statistics about the luca system',
          parameters: z.object({
            type: z
              .enum(['overview', 'growth', 'activity', 'health'])
              .describe('Type of analytics to retrieve'),
            days: z
              .number()
              .optional()
              .describe('Number of days for growth stats (default: 30)'),
          }),
          execute: async ({ type, days = 30 }) => {
            switch (type) {
              case 'overview':
                return await caller.analytics.getOverview();
              case 'growth':
                return await caller.analytics.getGrowthStats({ days });
              case 'activity':
                return await caller.analytics.getRecentActivity({ limit: 10 });
              case 'health':
                return await caller.analytics.getSystemHealth();
              default:
                return { error: 'Invalid analytics type' };
            }
          },
        }),

        searchKnowledge: tool({
          description:
            'Search the knowledge base for company information and documentation',
          parameters: z.object({
            query: z.string().describe('Search query for knowledge base'),
            limit: z
              .number()
              .optional()
              .describe('Number of results to return (default: 5)'),
          }),
          execute: async ({ query, limit = 5 }) => {
            const results = await caller.knowledge.search({ query, limit });
            return results;
          },
        }),

        complexAIQuery: tool({
          description:
            'Send complex AI queries to backend-python service for advanced processing',
          parameters: z.object({
            query: z
              .string()
              .describe('Complex query requiring advanced AI processing'),
            context: z
              .string()
              .optional()
              .describe('Additional context for the query'),
          }),
          execute: async ({ query, context }) => {
            try {
              // Proxy to backend-python service
              const backendUrl =
                process.env.BACKEND_PYTHON_URL || 'http://backend-python:8000';
              const response = await fetch(`${backendUrl}/api/ai/query`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query, context }),
              });

              if (!response.ok) {
                return { error: 'Backend AI service unavailable' };
              }

              const result = await response.json();
              return result;
            } catch (error) {
              console.error('Backend AI proxy error:', error);
              return { error: 'Failed to connect to advanced AI service' };
            }
          },
        }),
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Chat API error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
