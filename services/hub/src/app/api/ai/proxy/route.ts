import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { query, context, endpoint = 'query' } = body;

    // Proxy to backend-python service
    const backendUrl = process.env.BACKEND_PYTHON_URL || 'http://backend-python:8000';
    const response = await fetch(`${backendUrl}/api/ai/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward any authorization headers
        ...(req.headers.get('authorization') && {
          'Authorization': req.headers.get('authorization')!,
        }),
      },
      body: JSON.stringify({ query, context }),
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Backend AI service unavailable', status: response.status },
        { status: response.status }
      );
    }

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    console.error('AI proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to connect to AI service' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Health check endpoint
    const backendUrl = process.env.BACKEND_PYTHON_URL || 'http://backend-python:8000';
    const response = await fetch(`${backendUrl}/health`, {
      method: 'GET',
    });

    if (!response.ok) {
      return NextResponse.json(
        { status: 'unhealthy', backend: 'unavailable' },
        { status: 503 }
      );
    }

    const result = await response.json();
    return NextResponse.json({
      status: 'healthy',
      backend: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('AI proxy health check error:', error);
    return NextResponse.json(
      { status: 'unhealthy', error: 'Connection failed' },
      { status: 503 }
    );
  }
}
