'use client';

import { useQuery } from '@tanstack/react-query';
import { getMe } from '@/network/web-api';
import { UseGetMeReturn, QUERY_KEYS } from '@/types';

export function useGetMe(): UseGetMeReturn {
  const {
    data: user,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: [QUERY_KEYS.ME],
    queryFn: getMe,
    retry: false,
  });

  return {
    user: user || null,
    loading,
    error: error as Error | null,
  };
}
