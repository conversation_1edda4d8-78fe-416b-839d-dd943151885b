'use client';

import { useState, useEffect } from 'react';
import { SupportedLanguageEnum, getBrowserOrDefaultLanguage } from '@/utils/language';

const LANGUAGE_STORAGE_KEY = 'hub-language';

export function useLanguage() {
  const [currentLocale, setCurrentLocaleState] = useState<SupportedLanguageEnum>(
    getBrowserOrDefaultLanguage()
  );

  // Load from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (stored && Object.values(SupportedLanguageEnum).includes(stored as SupportedLanguageEnum)) {
        setCurrentLocaleState(stored as SupportedLanguageEnum);
      }
    }
  }, []);

  const setCurrentLocale = (locale: SupportedLanguageEnum) => {
    setCurrentLocaleState(locale);
    if (typeof window !== 'undefined') {
      localStorage.setItem(LANGUAGE_STORAGE_KEY, locale);
    }
  };

  return {
    currentLocale,
    setCurrentLocale,
  };
}
