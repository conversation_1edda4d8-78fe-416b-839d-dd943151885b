'use client';

import { useIntl } from 'react-intl';
import { LoginButton } from '@/components/auth/login-button';
import { MainPageLogoutButton } from '@/components/auth/main-page-logout-button';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loading } from '@/components/ui/loading';
import Link from 'next/link';
import { useGetMe } from '@/hooks/use-get-me';

export function AuthenticationSection() {
  const intl = useIntl();
  const { user, loading } = useGetMe();
  const isAuthenticated = !!user;

  if (loading) {
    return (
      <div className="max-w-md mx-auto">
        <Loading
          message={intl.formatMessage({ id: 'auth.loading' })}
          fullScreen={false}
        />
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto">
      <Card>
        <CardHeader className="text-center">
          <CardTitle>
            {isAuthenticated
              ? intl.formatMessage({ id: 'auth.loggedInAs' })
              : intl.formatMessage({ id: 'auth.login' })}
          </CardTitle>
          {isAuthenticated && user && (
            <CardDescription>{user.email}</CardDescription>
          )}
        </CardHeader>
        <CardContent>
          {isAuthenticated ? (
            <div className="flex flex-col gap-3">
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">
                  {intl.formatMessage({ id: 'navigation.dashboard' })}
                </Button>
              </Link>
              <MainPageLogoutButton />
            </div>
          ) : (
            <LoginButton />
          )}
        </CardContent>
      </Card>
    </div>
  );
}
