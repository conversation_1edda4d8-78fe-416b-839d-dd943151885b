'use client';

import { useIntl } from 'react-intl';
import { Button } from '@/components/ui/button';
import { useLogout } from './logout-button';

// Main page logout button (styled as a full-width outline button)
export function MainPageLogoutButton() {
  const intl = useIntl();
  const { handleLogout, isLoading } = useLogout();

  return (
    <Button
      variant="outline"
      className="w-full"
      onClick={handleLogout}
      disabled={isLoading}
    >
      {isLoading
        ? intl.formatMessage({ id: 'auth.loggingOut' })
        : intl.formatMessage({ id: 'auth.logout' })}
    </Button>
  );
}
