'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';
import { clearJwt } from '@/utils/jwt-storage';
import { LOGIN_REDIRECT } from '@/constants/routes';
import { Button } from '@/components/ui/button';

export function LoginButton() {
  const intl = useIntl();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = () => {
    setIsLoading(true);
    clearJwt();
    // Redirect to backend OIDC login endpoint
    window.location.href = LOGIN_REDIRECT;
  };

  return (
    <Button onClick={handleLogin} disabled={isLoading} className="w-full">
      {isLoading
        ? intl.formatMessage({ id: 'auth.loggingIn' })
        : intl.formatMessage({ id: 'auth.loginButton' })}
    </Button>
  );
}
