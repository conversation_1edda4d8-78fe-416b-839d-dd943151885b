'use client';

import { useIntl } from 'react-intl';
import { useGetMe } from '@/hooks/use-get-me';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function UserInfo() {
  const intl = useIntl();
  const { user } = useGetMe();

  if (!user) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {intl.formatMessage({ id: 'dashboard.userInfo' })}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div>
          <strong>{intl.formatMessage({ id: 'dashboard.email' })}:</strong>
          {user.email}
        </div>
        {user.name && (
          <div>
            <strong>{intl.formatMessage({ id: 'dashboard.name' })}:</strong>
            {user.name}
          </div>
        )}
        {user.roles && user.roles.length > 0 && (
          <div>
            <strong>{intl.formatMessage({ id: 'dashboard.roles' })}:</strong>
            {user.roles.join(', ')}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
