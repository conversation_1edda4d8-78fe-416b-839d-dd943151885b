'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';
import { useQueryClient } from '@tanstack/react-query';
import { clearJwt } from '@/utils/jwt-storage';
import { logoutSession } from '@/network/web-api';

// Shared logout logic hook
export function useLogout() {
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      clearJwt();
      queryClient.clear(); // Clear all cached queries
      await logoutSession();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      // Redirect to hub home page
      window.location.href = '/hub/';
    }
  };

  return { handleLogout, isLoading };
}

// Sidebar logout button (used in dropdown menu)
export function LogoutButton() {
  const intl = useIntl();
  const { handleLogout, isLoading } = useLogout();

  return (
    <span
      onClick={handleLogout}
      className="flex items-center justify-start w-full cursor-pointer"
      role="button"
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleLogout();
        }
      }}
    >
      {isLoading
        ? intl.formatMessage({ id: 'auth.loggingOut' })
        : intl.formatMessage({ id: 'auth.logout' })}
    </span>
  );
}
