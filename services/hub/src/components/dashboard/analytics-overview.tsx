'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { trpc } from '../../lib/trpc-client';
import { Loader2, Users, MapPin, Activity, Database } from 'lucide-react';

export function AnalyticsOverview() {
  const { data: overview, isLoading: overviewLoading } = trpc.analytics.getOverview.useQuery();
  const { data: health, isLoading: healthLoading } = trpc.analytics.getSystemHealth.useQuery();
  const { data: growth } = trpc.analytics.getGrowthStats.useQuery({ days: 30 });

  if (overviewLoading || healthLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{overview?.users?.toLocaleString() || 0}</div>
          {growth && (
            <p className="text-xs text-muted-foreground">
              +{growth.newUsers} in the last 30 days
            </p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Locations</CardTitle>
          <MapPin className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{overview?.locations?.toLocaleString() || 0}</div>
          {growth && (
            <p className="text-xs text-muted-foreground">
              +{growth.newLocations} in the last 30 days
            </p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{overview?.sessions?.toLocaleString() || 0}</div>
          {growth && (
            <p className="text-xs text-muted-foreground">
              +{growth.newSessions} in the last 30 days
            </p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">System Health</CardTitle>
          <Database className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {health?.database === 'healthy' && 
             health?.redis === 'healthy' && 
             health?.weaviate === 'healthy' ? '✅' : '⚠️'}
          </div>
          <p className="text-xs text-muted-foreground">
            DB: {health?.database || 'unknown'} | 
            Redis: {health?.redis || 'unknown'} | 
            Vector: {health?.weaviate || 'unknown'}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
