'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { trpc } from '../../lib/trpc-client';
import { Loader2, User, MapPin } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export function RecentActivity() {
  const { data: activity, isLoading } = trpc.analytics.getRecentActivity.useQuery({ limit: 10 });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest system activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Latest system activity</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activity?.map((item) => (
            <div key={`${item.type}-${item.id}`} className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                {item.type === 'user' ? (
                  <User className="h-4 w-4 text-blue-500" />
                ) : (
                  <MapPin className="h-4 w-4 text-green-500" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  New {item.type} created
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  ID: {item.id}
                </p>
              </div>
              <div className="flex-shrink-0 text-sm text-gray-500 dark:text-gray-400">
                {formatDistanceToNow(new Date(item.createdAt), { addSuffix: true })}
              </div>
            </div>
          ))}
          {(!activity || activity.length === 0) && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">
              No recent activity
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
