'use client';

import { useIntl } from 'react-intl';
import { useGetMe } from '@/hooks/use-get-me';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loading } from '@/components/ui/loading';
import { LoginButton } from './login-button';
import { LanguageSwitcher } from '@/components/ui/language-switcher';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const intl = useIntl();
  const { user, loading, error } = useGetMe();

  if (loading) {
    return (
      <Loading
        message={intl.formatMessage({ id: 'auth.loading' })}
        fullScreen
      />
    );
  }

  if (error || !user) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-6 py-16">
          {/* Header with language switcher */}
          <div className="flex justify-between items-center mb-16">
            <div className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <span className="text-sm font-semibold">l</span>
              </div>
              <span className="text-lg font-semibold">
                {intl.formatMessage({ id: 'app.title' })}
              </span>
            </div>
            <LanguageSwitcher />
          </div>

          <div className="max-w-2xl mx-auto text-center mb-16">
            <h1 className="text-4xl font-medium mb-6">
              {intl.formatMessage({ id: 'auth.loginRequired' })}
            </h1>
            <p className="text-xl text-muted-foreground">
              {intl.formatMessage({ id: 'auth.loginRequiredDescription' })}
            </p>
          </div>

          {/* Authentication Section */}
          <div className="max-w-md mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle>
                  {intl.formatMessage({ id: 'auth.login' })}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <LoginButton />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
