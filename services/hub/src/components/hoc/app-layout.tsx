'use client';

import { LogOut, Globe, Monitor, User, ChevronUp } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTheme } from 'next-themes';

import { useGetMe } from '@/hooks/use-get-me';
import { getPrimaryRole } from '@/utils/roles';
import { LogoutButton } from '@/components/auth/logout-button';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { LucaLogo } from '@/components/ui/luca-logo';
import { useLanguage } from '@/hooks/use-language';
import { SupportedLanguageEnum } from '@/utils/language';
import { navigationItems } from '@/constants/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const { user } = useGetMe();
  const { setTheme } = useTheme();
  const { setCurrentLocale, currentLocale } = useLanguage();
  const pathname = usePathname();

  const userInitials = user?.name
    ? user.name
        .split(' ')
        .map((n: string) => n[0])
        .join('')
        .toUpperCase()
    : user?.email?.charAt(0).toUpperCase() || 'U';

  const primaryRole = getPrimaryRole(user?.roles);

  const toggleTheme = () => {
    const currentTheme = document.documentElement.classList.contains('dark')
      ? 'dark'
      : 'light';
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  };

  const toggleLanguage = () => {
    const newLocale =
      currentLocale === SupportedLanguageEnum.en
        ? SupportedLanguageEnum.de
        : SupportedLanguageEnum.en;
    setCurrentLocale(newLocale);
  };

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <Sidebar collapsible="icon" className="border-r">
          <SidebarHeader className="border-b">
            <div className="flex h-8 items-center gap-2 rounded-md p-2 text-sm">
              <div className="flex h-4 w-4 items-center justify-center shrink-0">
                <LucaLogo className="h-4 w-4 text-foreground" />
              </div>
              <span className="font-medium text-muted-foreground group-data-[collapsible=icon]:hidden">
                {primaryRole}
              </span>
            </div>
          </SidebarHeader>

          <SidebarContent>
            {navigationItems.map(group => (
              <SidebarGroup key={group.title}>
                <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
                <SidebarGroupContent>
                  <SidebarMenu>
                    {group.items.map(item => {
                      const isActive = pathname === item.url;
                      return (
                        <SidebarMenuItem key={item.title}>
                          <SidebarMenuButton asChild isActive={isActive}>
                            <Link href={item.url}>
                              <item.icon className="h-4 w-4" />
                              <span>{item.title}</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      );
                    })}
                  </SidebarMenu>
                </SidebarGroupContent>
              </SidebarGroup>
            ))}
          </SidebarContent>

          <SidebarFooter className="border-t">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full justify-start gap-2 rounded-md p-2 text-sm group-data-[collapsible=icon]:justify-center"
                >
                  <div className="flex h-4 w-4 items-center justify-center shrink-0">
                    <Avatar className="h-4 w-4">
                      <AvatarImage src="" alt={user?.name || user?.email} />
                      <AvatarFallback className="text-xs">
                        {userInitials}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="flex flex-col items-start text-left group-data-[collapsible=icon]:hidden">
                    <span className="text-sm font-medium">
                      {user?.name || user?.email}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto h-4 w-4 group-data-[collapsible=icon]:hidden" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="right" align="start" className="w-56">
                <DropdownMenuItem asChild>
                  <Link href="/profile" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>Profile</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="flex items-center justify-between cursor-pointer"
                  onClick={toggleTheme}
                >
                  <div className="flex items-center gap-2">
                    <Monitor className="h-4 w-4" />
                    <span>Theme</span>
                  </div>
                  <ThemeToggle />
                </DropdownMenuItem>
                <DropdownMenuItem
                  className="flex items-center justify-between cursor-pointer"
                  onClick={toggleLanguage}
                >
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    <span>Language</span>
                  </div>
                  <LanguageSwitcher />
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <div className="flex items-center gap-2 cursor-pointer w-full">
                    <LogOut className="h-4 w-4" />
                    <span className="flex-1 text-left">
                      <LogoutButton />
                    </span>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarFooter>
        </Sidebar>

        <div className="flex-1 flex flex-col">
          <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-2">
            <div className="flex items-center gap-2 rounded-md p-2">
              <div className="flex h-4 w-4 items-center justify-center shrink-0">
                <SidebarTrigger className="h-4 w-4 p-0" />
              </div>
              <div className="flex-1" />
            </div>
          </header>
          <main className="flex-1 p-6">{children}</main>
        </div>
      </div>
    </SidebarProvider>
  );
}
