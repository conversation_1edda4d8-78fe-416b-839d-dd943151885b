'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { trpc } from '../../lib/trpc-client';
import { Loader2, Search, FileText } from 'lucide-react';

export function KnowledgeSearch() {
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any>(null);
  const [isSearching, setIsSearching] = useState(false);

  const searchMutation = trpc.knowledge.search.useMutation({
    onSuccess: (data) => {
      setSearchResults(data);
      setIsSearching(false);
    },
    onError: (error) => {
      console.error('Search error:', error);
      setIsSearching(false);
    },
  });

  const handleSearch = async () => {
    if (!query.trim()) return;
    
    setIsSearching(true);
    searchMutation.mutate({ query, limit: 10 });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Knowledge Base Search
        </CardTitle>
        <CardDescription>
          Search company documentation and knowledge base
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Input
            placeholder="Search knowledge base..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
          />
          <Button onClick={handleSearch} disabled={isSearching || !query.trim()}>
            {isSearching ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Search className="h-4 w-4" />
            )}
          </Button>
        </div>

        {searchResults && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">
                Search Results ({searchResults.results?.length || 0})
              </h3>
              <Badge variant="outline">
                Source: {searchResults.source}
              </Badge>
            </div>

            <div className="space-y-3">
              {searchResults.results?.map((result: any, index: number) => (
                <div
                  key={result.id || index}
                  className="border rounded-lg p-4 space-y-2"
                >
                  <div className="flex items-start justify-between">
                    <h4 className="font-medium flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {result.title}
                    </h4>
                    {result.category && (
                      <Badge variant="secondary" className="text-xs">
                        {result.category}
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
                    {result.content}
                  </p>
                  
                  {result.source && (
                    <p className="text-xs text-gray-500">
                      Source: {result.source}
                    </p>
                  )}
                  
                  {result._additional?.score && (
                    <p className="text-xs text-gray-500">
                      Relevance: {(result._additional.score * 100).toFixed(1)}%
                    </p>
                  )}
                </div>
              ))}
              
              {searchResults.results?.length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">
                  No results found for "{query}"
                </p>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
