'use client';

import { IntlProvider as ReactIntlProvider } from 'react-intl';
import { useLanguage } from '@/hooks/use-language';
import { messages } from '@/messages';

interface IntlProviderProps {
  children: React.ReactNode;
}

export function IntlProvider({ children }: IntlProviderProps) {
  const { currentLocale } = useLanguage();

  return (
    <ReactIntlProvider
      locale={currentLocale}
      messages={messages[currentLocale]}
      wrapRichTextChunksInFragment
    >
      {children}
    </ReactIntlProvider>
  );
}
