'use client';

import { useTheme } from 'next-themes';
import { useIntl } from 'react-intl';
import { useEffect, useState } from 'react';

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  const intl = useIntl();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <span className="text-sm">...</span>;
  }

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  const currentThemeLabel =
    theme === 'dark'
      ? intl.formatMessage({ id: 'theme.dark' })
      : intl.formatMessage({ id: 'theme.light' });

  return (
    <button
      onClick={toggleTheme}
      data-theme-toggle
      className="text-sm px-2 py-1 rounded hover:bg-accent"
    >
      {currentThemeLabel}
    </button>
  );
}
