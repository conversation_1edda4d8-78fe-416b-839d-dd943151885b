'use client';

interface LoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

export function Loading({
  message = 'Loading...',
  size = 'md',
  fullScreen = false
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const containerClasses = fullScreen
    ? 'min-h-screen flex items-center justify-center'
    : 'flex items-center justify-center p-4';

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <div
          className={`animate-spin rounded-full border-b-2 border-gray-900 dark:border-white mx-auto ${sizeClasses[size]}`}
        />
        {message && (
          <p className="mt-2 text-gray-600 dark:text-gray-300">{message}</p>
        )}
      </div>
    </div>
  );
}
