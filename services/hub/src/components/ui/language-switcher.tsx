'use client';

import { useIntl } from 'react-intl';
import { useLanguage } from '@/hooks/use-language';
import { SupportedLanguageEnum } from '@/utils/language';

export function LanguageSwitcher() {
  const intl = useIntl();
  const { currentLocale, setCurrentLocale } = useLanguage();

  const toggleLanguage = () => {
    const newLocale =
      currentLocale === SupportedLanguageEnum.en
        ? SupportedLanguageEnum.de
        : SupportedLanguageEnum.en;
    setCurrentLocale(newLocale);
  };

  const getLanguageLabel = (locale: SupportedLanguageEnum) => {
    return locale === SupportedLanguageEnum.en
      ? intl.formatMessage({ id: 'language.en' })
      : intl.formatMessage({ id: 'language.de' });
  };

  return (
    <button
      onClick={toggleLanguage}
      data-language-toggle
      className="text-sm px-2 py-1 rounded hover:bg-accent"
    >
      {getLanguageLabel(currentLocale)}
    </button>
  );
}
