import { z } from 'zod';
import { eq, like, desc, count } from 'drizzle-orm';
import { router, publicProcedure } from '../../lib/trpc';
import { users } from '../../lib/schema';

export const usersRouter = router({
  // Get user by ID
  getById: publicProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      const user = await ctx.db
        .select()
        .from(users)
        .where(eq(users.uuid, input))
        .limit(1);
      
      return user[0] || null;
    }),

  // Search users
  search: publicProcedure
    .input(z.object({
      query: z.string().min(1),
      limit: z.number().min(1).max(50).default(10),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      const { query, limit, offset } = input;
      
      // Search in data field (which contains encrypted user info)
      // Note: This is a basic search - in production you might want to decrypt and search properly
      const results = await ctx.db
        .select()
        .from(users)
        .where(like(users.data, `%${query}%`))
        .limit(limit)
        .offset(offset)
        .orderBy(desc(users.createdAt));
      
      return results;
    }),

  // Get user statistics
  getStats: publicProcedure
    .query(async ({ ctx }) => {
      const [totalUsers] = await ctx.db
        .select({ count: count() })
        .from(users);
      
      // Get users created in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const [recentUsers] = await ctx.db
        .select({ count: count() })
        .from(users)
        .where(eq(users.createdAt, thirtyDaysAgo));
      
      return {
        total: totalUsers.count,
        recent: recentUsers.count,
      };
    }),

  // Get recent users
  getRecent: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(20).default(10),
    }))
    .query(async ({ input, ctx }) => {
      const results = await ctx.db
        .select()
        .from(users)
        .orderBy(desc(users.createdAt))
        .limit(input.limit);
      
      return results;
    }),
});
