import { z } from 'zod';
import { eq, desc, like } from 'drizzle-orm';
import { router, publicProcedure } from '../../lib/trpc';
import { knowledgeDocuments } from '../../lib/schema';
import { searchKnowledgeBase, addToKnowledgeBase } from '../../lib/weaviate';

export const knowledgeRouter = router({
  // Search knowledge base using vector search
  search: publicProcedure
    .input(z.object({
      query: z.string().min(1),
      limit: z.number().min(1).max(20).default(5),
    }))
    .query(async ({ input, ctx }) => {
      const { query, limit } = input;
      
      try {
        // First try vector search with Weaviate
        const vectorResults = await searchKnowledgeBase(query, limit);
        
        if (vectorResults.length > 0) {
          return {
            results: vectorResults,
            source: 'vector',
          };
        }
        
        // Fallback to database text search
        const dbResults = await ctx.db
          .select()
          .from(knowledgeDocuments)
          .where(like(knowledgeDocuments.content, `%${query}%`))
          .limit(limit)
          .orderBy(desc(knowledgeDocuments.createdAt));
        
        return {
          results: dbResults,
          source: 'database',
        };
      } catch (error) {
        console.error('Knowledge search error:', error);
        
        // Fallback to database search
        const dbResults = await ctx.db
          .select()
          .from(knowledgeDocuments)
          .where(like(knowledgeDocuments.content, `%${query}%`))
          .limit(limit)
          .orderBy(desc(knowledgeDocuments.createdAt));
        
        return {
          results: dbResults,
          source: 'database',
        };
      }
    }),

  // Add document to knowledge base
  addDocument: publicProcedure
    .input(z.object({
      title: z.string().min(1).max(255),
      content: z.string().min(1),
      category: z.string().max(100).optional(),
      source: z.string().max(255).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      const { title, content, category, source } = input;
      
      try {
        // Add to Weaviate first
        const weaviateResult = await addToKnowledgeBase({
          title,
          content,
          category: category || 'general',
          source: source || 'manual',
        });
        
        // Then add to database
        const [dbResult] = await ctx.db
          .insert(knowledgeDocuments)
          .values({
            title,
            content,
            category,
            source,
            weaviateId: weaviateResult.id,
          })
          .returning();
        
        return dbResult;
      } catch (error) {
        console.error('Error adding document:', error);
        throw new Error('Failed to add document to knowledge base');
      }
    }),

  // Get all documents
  getAll: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(20),
      offset: z.number().min(0).default(0),
      category: z.string().optional(),
    }))
    .query(async ({ input, ctx }) => {
      const { limit, offset, category } = input;
      
      let query = ctx.db
        .select()
        .from(knowledgeDocuments);
      
      if (category) {
        query = query.where(eq(knowledgeDocuments.category, category));
      }
      
      const results = await query
        .limit(limit)
        .offset(offset)
        .orderBy(desc(knowledgeDocuments.createdAt));
      
      return results;
    }),

  // Get document by ID
  getById: publicProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      const document = await ctx.db
        .select()
        .from(knowledgeDocuments)
        .where(eq(knowledgeDocuments.id, input))
        .limit(1);
      
      return document[0] || null;
    }),

  // Delete document
  delete: publicProcedure
    .input(z.string().uuid())
    .mutation(async ({ input, ctx }) => {
      // TODO: Also delete from Weaviate
      const result = await ctx.db
        .delete(knowledgeDocuments)
        .where(eq(knowledgeDocuments.id, input))
        .returning();
      
      return result[0] || null;
    }),
});
