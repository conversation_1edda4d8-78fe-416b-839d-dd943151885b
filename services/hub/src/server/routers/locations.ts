import { z } from 'zod';
import { eq, like, desc, count } from 'drizzle-orm';
import { router, publicProcedure } from '../../lib/trpc';
import { locations, locationGroups } from '../../lib/schema';

export const locationsRouter = router({
  // Get location by ID
  getById: publicProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      const location = await ctx.db
        .select()
        .from(locations)
        .where(eq(locations.uuid, input))
        .limit(1);
      
      return location[0] || null;
    }),

  // Search locations
  search: publicProcedure
    .input(z.object({
      query: z.string().min(1),
      limit: z.number().min(1).max(50).default(10),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      const { query, limit, offset } = input;
      
      const results = await ctx.db
        .select()
        .from(locations)
        .where(like(locations.data, `%${query}%`))
        .limit(limit)
        .offset(offset)
        .orderBy(desc(locations.createdAt));
      
      return results;
    }),

  // Get location statistics
  getStats: publicProcedure
    .query(async ({ ctx }) => {
      const [totalLocations] = await ctx.db
        .select({ count: count() })
        .from(locations);
      
      const [totalLocationGroups] = await ctx.db
        .select({ count: count() })
        .from(locationGroups);
      
      return {
        totalLocations: totalLocations.count,
        totalLocationGroups: totalLocationGroups.count,
      };
    }),

  // Get recent locations
  getRecent: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(20).default(10),
    }))
    .query(async ({ input, ctx }) => {
      const results = await ctx.db
        .select()
        .from(locations)
        .orderBy(desc(locations.createdAt))
        .limit(input.limit);
      
      return results;
    }),

  // Get location groups
  getLocationGroups: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      const { limit, offset } = input;
      
      const results = await ctx.db
        .select()
        .from(locationGroups)
        .limit(limit)
        .offset(offset)
        .orderBy(desc(locationGroups.createdAt));
      
      return results;
    }),
});
