import { z } from 'zod';
import { count, desc, gte } from 'drizzle-orm';
import { router, publicProcedure } from '../../lib/trpc';
import { users, locations, locationGroups, sessions } from '../../lib/schema';

export const analyticsRouter = router({
  // Get overview statistics
  getOverview: publicProcedure
    .query(async ({ ctx }) => {
      const [totalUsers] = await ctx.db
        .select({ count: count() })
        .from(users);
      
      const [totalLocations] = await ctx.db
        .select({ count: count() })
        .from(locations);
      
      const [totalLocationGroups] = await ctx.db
        .select({ count: count() })
        .from(locationGroups);
      
      const [totalSessions] = await ctx.db
        .select({ count: count() })
        .from(sessions);
      
      return {
        users: totalUsers.count,
        locations: totalLocations.count,
        locationGroups: totalLocationGroups.count,
        sessions: totalSessions.count,
      };
    }),

  // Get growth statistics
  getGrowthStats: publicProcedure
    .input(z.object({
      days: z.number().min(1).max(365).default(30),
    }))
    .query(async ({ input, ctx }) => {
      const { days } = input;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      
      const [newUsers] = await ctx.db
        .select({ count: count() })
        .from(users)
        .where(gte(users.createdAt, startDate));
      
      const [newLocations] = await ctx.db
        .select({ count: count() })
        .from(locations)
        .where(gte(locations.createdAt, startDate));
      
      const [newSessions] = await ctx.db
        .select({ count: count() })
        .from(sessions)
        .where(gte(sessions.createdAt, startDate));
      
      return {
        period: `${days} days`,
        newUsers: newUsers.count,
        newLocations: newLocations.count,
        newSessions: newSessions.count,
      };
    }),

  // Get recent activity
  getRecentActivity: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ input, ctx }) => {
      const { limit } = input;
      
      // Get recent users
      const recentUsers = await ctx.db
        .select({
          id: users.uuid,
          type: 'user' as const,
          createdAt: users.createdAt,
        })
        .from(users)
        .orderBy(desc(users.createdAt))
        .limit(Math.floor(limit / 2));
      
      // Get recent locations
      const recentLocations = await ctx.db
        .select({
          id: locations.uuid,
          type: 'location' as const,
          createdAt: locations.createdAt,
        })
        .from(locations)
        .orderBy(desc(locations.createdAt))
        .limit(Math.floor(limit / 2));
      
      // Combine and sort
      const combined = [...recentUsers, ...recentLocations]
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, limit);
      
      return combined;
    }),

  // Get system health metrics
  getSystemHealth: publicProcedure
    .query(async ({ ctx }) => {
      try {
        // Test database connection
        const [dbTest] = await ctx.db
          .select({ count: count() })
          .from(users)
          .limit(1);
        
        // Test Redis connection
        const redisTest = await ctx.redis.ping();
        
        // Test Weaviate connection (basic)
        let weaviateStatus = 'unknown';
        try {
          await ctx.weaviate.misc.liveChecker().do();
          weaviateStatus = 'healthy';
        } catch {
          weaviateStatus = 'unhealthy';
        }
        
        return {
          database: 'healthy',
          redis: redisTest === 'PONG' ? 'healthy' : 'unhealthy',
          weaviate: weaviateStatus,
          timestamp: new Date().toISOString(),
        };
      } catch (error) {
        console.error('Health check error:', error);
        return {
          database: 'unhealthy',
          redis: 'unknown',
          weaviate: 'unknown',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error',
        };
      }
    }),
});
