import { router } from '../../lib/trpc';
import { usersRouter } from './users';
import { locationsRouter } from './locations';
import { knowledgeRouter } from './knowledge';
import { analyticsRouter } from './analytics';

export const appRouter = router({
  users: usersRouter,
  locations: locationsRouter,
  knowledge: knowledgeRouter,
  analytics: analyticsRouter,
});

export type AppRouter = typeof appRouter;
