export class ApiException extends Error {
  constructor(public status: number, message: string, public code?: string) {
    super(message);
    this.name = 'ApiException';
  }
}

export const handleApiResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    let errorMessage = `Request failed with status ${response.status}`;
    let errorCode: string | undefined;

    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
      errorCode = errorData.code;
    } catch {
      // If we can't parse the error response, use the default message
    }

    throw new ApiException(response.status, errorMessage, errorCode);
  }

  try {
    return await response.json();
  } catch {
    // If response is not JSON, return empty object
    return {} as T;
  }
};

export const getRequest = async <T = unknown>(
  url: string,
  headers: Record<string, string>
): Promise<T> => {
  const response = await fetch(url, { headers });
  return handleApiResponse<T>(response);
};

export const postRequest = async <T = unknown>(
  url: string,
  data?: unknown,
  headers: Record<string, string> = {}
): Promise<T> => {
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    body: data ? JSON.stringify(data) : undefined,
  });
  return handleApiResponse<T>(response);
};

export const putRequest = async <T = unknown>(
  url: string,
  data?: unknown,
  headers: Record<string, string> = {}
): Promise<T> => {
  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
    body: data ? JSON.stringify(data) : undefined,
  });
  return handleApiResponse<T>(response);
};

export const deleteRequest = async <T = unknown>(
  url: string,
  headers: Record<string, string> = {}
): Promise<T> => {
  const response = await fetch(url, {
    method: 'DELETE',
    headers,
  });
  return handleApiResponse<T>(response);
};
