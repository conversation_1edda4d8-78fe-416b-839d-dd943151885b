import { getRequest, handleApiResponse } from './utils';
import { API_ENDPOINTS, DEFAULT_HEADERS } from '@/constants/api';
import { User } from '@/types/user';

export const getBackendJwt = (): Promise<string> =>
  getRequest<string>(API_ENDPOINTS.AUTH.JWT, DEFAULT_HEADERS);

export const getMe = async (): Promise<User> => {
  return getRequest<User>(API_ENDPOINTS.AUTH.ME, DEFAULT_HEADERS);
};

export const logoutSession = async (): Promise<void> => {
  const response = await fetch(API_ENDPOINTS.AUTH.LOGOUT, {
    method: 'POST',
    headers: DEFAULT_HEADERS,
  });
  await handleApiResponse<void>(response);
};
