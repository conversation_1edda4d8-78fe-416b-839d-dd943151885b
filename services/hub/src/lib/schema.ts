import { pgTable, uuid, varchar, text, timestamp, integer, boolean, jsonb } from 'drizzle-orm/pg-core';

// Users table (from backend database)
export const users = pgTable('Users', {
  uuid: uuid('uuid').primaryKey().defaultRandom(),
  publicKey: varchar('publicKey', { length: 88 }),
  data: varchar('data', { length: 1024 }).notNull(),
  iv: varchar('iv'),
  mac: varchar('mac'),
  signature: varchar('signature'),
  deviceType: varchar('deviceType'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
  deletedAt: timestamp('deletedAt'),
});

// Locations table (from backend database)
export const locations = pgTable('Locations', {
  uuid: uuid('uuid').primaryKey().defaultRandom(),
  publicKey: varchar('publicKey', { length: 88 }),
  data: varchar('data', { length: 2048 }).notNull(),
  iv: varchar('iv'),
  mac: varchar('mac'),
  signature: varchar('signature'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
  deletedAt: timestamp('deletedAt'),
});

// Location Groups table (from backend database)
export const locationGroups = pgTable('LocationGroups', {
  uuid: uuid('uuid').primaryKey().defaultRandom(),
  publicKey: varchar('publicKey', { length: 88 }),
  data: varchar('data', { length: 2048 }).notNull(),
  iv: varchar('iv'),
  mac: varchar('mac'),
  signature: varchar('signature'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
  deletedAt: timestamp('deletedAt'),
});

// Operators table (from backend database)
export const operators = pgTable('Operators', {
  uuid: uuid('uuid').primaryKey().defaultRandom(),
  publicKey: varchar('publicKey', { length: 88 }),
  data: varchar('data', { length: 2048 }).notNull(),
  iv: varchar('iv'),
  mac: varchar('mac'),
  signature: varchar('signature'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
  deletedAt: timestamp('deletedAt'),
});

// Sessions table (from backend database)
export const sessions = pgTable('Sessions', {
  uuid: uuid('uuid').primaryKey().defaultRandom(),
  publicKey: varchar('publicKey', { length: 88 }),
  data: varchar('data', { length: 1024 }).notNull(),
  iv: varchar('iv'),
  mac: varchar('mac'),
  signature: varchar('signature'),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
  deletedAt: timestamp('deletedAt'),
});

// Chat sessions table (new for hub service)
export const chatSessions = pgTable('ChatSessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('userId').references(() => users.uuid),
  title: varchar('title', { length: 255 }),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
});

// Chat messages table (new for hub service)
export const chatMessages = pgTable('ChatMessages', {
  id: uuid('id').primaryKey().defaultRandom(),
  sessionId: uuid('sessionId').references(() => chatSessions.id),
  role: varchar('role', { length: 20 }).notNull(), // 'user' | 'assistant' | 'system'
  content: text('content').notNull(),
  metadata: jsonb('metadata'), // For storing additional data like function calls, etc.
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});

// Knowledge base documents table (new for hub service)
export const knowledgeDocuments = pgTable('KnowledgeDocuments', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  category: varchar('category', { length: 100 }),
  source: varchar('source', { length: 255 }),
  weaviateId: varchar('weaviateId', { length: 255 }), // Reference to Weaviate object
  createdAt: timestamp('createdAt').defaultNow().notNull(),
  updatedAt: timestamp('updatedAt').defaultNow(),
});

export type User = typeof users.$inferSelect;
export type Location = typeof locations.$inferSelect;
export type LocationGroup = typeof locationGroups.$inferSelect;
export type Operator = typeof operators.$inferSelect;
export type Session = typeof sessions.$inferSelect;
export type ChatSession = typeof chatSessions.$inferSelect;
export type ChatMessage = typeof chatMessages.$inferSelect;
export type KnowledgeDocument = typeof knowledgeDocuments.$inferSelect;
