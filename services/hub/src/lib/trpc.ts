import { initTRPC } from '@trpc/server';
import { z } from 'zod';
import superjson from 'superjson';
import { db } from './db';
import { redis } from './redis';
import { getWeaviateClient } from './weaviate';

// Create tRPC context
export const createTRPCContext = async (opts: { req?: Request }) => {
  return {
    db,
    redis,
    weaviate: getWeaviateClient(),
    req: opts.req,
  };
};

export type Context = Awaited<ReturnType<typeof createTRPCContext>>;

// Initialize tRPC
const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter({ shape }) {
    return shape;
  },
});

// Export reusable router and procedure helpers
export const router = t.router;
export const procedure = t.procedure;

// Middleware for logging
const loggerMiddleware = t.middleware(async ({ path, type, next }) => {
  const start = Date.now();
  const result = await next();
  const durationMs = Date.now() - start;
  
  console.log(`${type} ${path} - ${durationMs}ms`);
  
  return result;
});

// Base procedure with logging
export const publicProcedure = procedure.use(loggerMiddleware);

// Authenticated procedure (you can add auth logic here later)
export const protectedProcedure = publicProcedure.use(async ({ ctx, next }) => {
  // TODO: Add authentication logic here
  // For now, just pass through
  return next();
});
