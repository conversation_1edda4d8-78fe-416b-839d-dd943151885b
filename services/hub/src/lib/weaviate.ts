import weaviate, { WeaviateClient } from 'weaviate-ts-client';

let weaviateClient: WeaviateClient | null = null;

export function getWeaviateClient(): WeaviateClient {
  if (!weaviateClient) {
    weaviateClient = weaviate.client({
      scheme: 'https',
      host: process.env.WEAVIATE_HOST || 'localhost:8080',
      apiKey: process.env.WEAVIATE_API_KEY ? {
        apiKey: process.env.WEAVIATE_API_KEY,
      } : undefined,
      headers: process.env.WEAVIATE_API_KEY ? {
        'X-OpenAI-Api-Key': process.env.OPENAI_API_KEY || '',
      } : undefined,
    });
  }
  
  return weaviateClient;
}

// Knowledge base collection name
export const KNOWLEDGE_BASE_COLLECTION = 'LucaKnowledgeBase';

// Helper function to search knowledge base
export async function searchKnowledgeBase(query: string, limit: number = 5) {
  const client = getWeaviateClient();
  
  try {
    const result = await client.graphql
      .get()
      .withClassName(KNOWLEDGE_BASE_COLLECTION)
      .withFields('title content category source _additional { score }')
      .withNearText({ concepts: [query] })
      .withLimit(limit)
      .do();
    
    return result.data?.Get?.[KNOWLEDGE_BASE_COLLECTION] || [];
  } catch (error) {
    console.error('Weaviate search error:', error);
    return [];
  }
}

// Helper function to add document to knowledge base
export async function addToKnowledgeBase(document: {
  title: string;
  content: string;
  category: string;
  source: string;
}) {
  const client = getWeaviateClient();
  
  try {
    const result = await client.data
      .creator()
      .withClassName(KNOWLEDGE_BASE_COLLECTION)
      .withProperties(document)
      .do();
    
    return result;
  } catch (error) {
    console.error('Weaviate insert error:', error);
    throw error;
  }
}
