// Simple auth configuration that integrates with existing backend OIDC
export interface User {
  id: string;
  email: string;
  name?: string;
  roles?: string[];
}

export interface AuthSession {
  user: User | null;
  isAuthenticated: boolean;
}

// Auth utilities for integrating with existing backend
export const authConfig = {
  oidcLoginUrl: '/api/v4/auth/oidc/login',
  oidcLogoutUrl: '/api/v4/auth/logout',
  userInfoUrl: '/api/v4/auth/support/me',
  jwtUrl: '/api/v4/auth/jwt',
};
