# Luca Next

A modern Next.js 15 service integrated into the Luca ecosystem.

## Features

- **Next.js 15** with App Router and React Server Components
- **shadcn/ui** components with Tailwind CSS
- **TypeScript** for type safety
- **Docker** containerization for development and production
- **ELB routing** integration at `/hub`
- **Standalone output** for optimized Docker deployment
- **AI Assistant** powered by Vercel AI SDK and OpenAI
- **Internationalization** with React Intl (English/German)
- **Authentication** integration with existing OIDC system

## Development

### Local Development

```bash
# Install dependencies
yarn install

# Start development server
yarn dev
```

The service will be available at:

- Local: http://localhost:8080
- Through ELB: https://localhost/hub

### Environment Variables

For the AI Assistant feature, you'll need to set:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### Docker Development

```bash
# From the root of luca-web repository
yarn dev up hub
```

## Production

The service is configured for production deployment with:

- Standalone output mode for minimal Docker images
- Multi-stage Docker builds
- Non-root user for security
- Port 8080 for container networking

## Architecture

- **Base Path**: `/hub` for ELB routing
- **Port**: 8080 (internal container port)
- **External Port**: 8092 (mapped in docker-compose.local.yml)
- **Dependencies**: ELB for routing

## Integration

This service is fully integrated into the Luca infrastructure:

- Added to `workspace.json` for Nx workspace management
- Included in `scripts/yarnAll.sh` for dependency management
- Configured in all Docker Compose files
- ELB routing configured in `services/elb/nginx.conf`
