FROM harbor.nexenio.local/cache-dockerhub/library/node:22.16.0-alpine3.21 as builder

WORKDIR /app

# Install dependencies
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile && yarn cache clean

# Copy source files
COPY . .

# Build the application
ARG GIT_COMMIT
ARG GIT_VERSION
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN yarn build

# Production container
FROM harbor.nexenio.local/cache-dockerhub/library/node:22.16.0-alpine3.21

WORKDIR /app

# Upgrade packages (won't introduce breaking changes in alpine)
RUN apk upgrade --no-cache --available

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 8080

ENV PORT=8080
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
