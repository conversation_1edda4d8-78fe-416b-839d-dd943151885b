import { useIntl } from 'react-intl';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import {
  Layout,
  Topbar,
  SearchField,
  PageContentWrapper,
  PageHeaderWrapper,
  PageTitle,
  NormalText16,
} from 'components/general';
import { DASHBOARD_ROUTE, KIOSK_SETUP_ROUTE } from 'constants/routes';
import { isValidUUID } from 'utils/validation';
import { getPayGroupById } from 'network/payApi';
import { KioskSetupContainer } from 'containers';
import { SearchError } from 'containers/LocationGroupSettingsContainer';
import { ContentWrapper, SectionWrapper } from './KioskSetupPage.styled';
import { KioskContent } from './KioskContent';

export const KioskSetupPage = () => {
  const intl = useIntl();

  const {
    searchError,
    selectedPaymentGroup,
    setSearchError,
    setSelectedPaymentGroup,
    isSearchLoading,
    setIsSearchLoading,
    resetGroupSelection,
  } = KioskSetupContainer.useContainer();

  const onSearch = async ({
    locationGroupId,
  }: {
    locationGroupId?: string;
  }) => {
    setIsSearchLoading(true);
    setSearchError(null);
    setSelectedPaymentGroup(null);
    if (!isValidUUID(locationGroupId) || !locationGroupId) {
      setSearchError(SearchError.InvalidUuid);
      return;
    }

    try {
      const payGroup = await getPayGroupById({
        groupId: locationGroupId,
      });

      if (payGroup) {
        setSelectedPaymentGroup(payGroup);
      }
    } catch (error: unknown) {
      console.error(error);
      if (error instanceof Error && error.message.includes('404')) {
        setSearchError(SearchError.NotFound);
      } else {
        setSearchError(SearchError.NetworkError);
      }
    }

    setIsSearchLoading(false);
  };

  const hasSelectedPaymentGroup = !!selectedPaymentGroup;

  return (
    <Layout>
      <Topbar
        title={intl.formatMessage({
          id: hasSelectedPaymentGroup ? 'search' : 'overview',
        })}
        pathName={hasSelectedPaymentGroup ? KIOSK_SETUP_ROUTE : DASHBOARD_ROUTE}
        callBack={() => resetGroupSelection()}
      />
      <PageContentWrapper>
        {!hasSelectedPaymentGroup && (
          <PageHeaderWrapper>
            <PageTitle>
              {intl.formatMessage({
                id: 'dashboardItem.kioskSetup',
              })}
            </PageTitle>
            <SearchField
              defaultValue="67733c1e-6d74-40bd-a62e-2eaae8168752"
              style={{ marginBottom: 24 }}
              placeholder="groupId"
              onSearch={value => onSearch({ locationGroupId: value })}
            />
          </PageHeaderWrapper>
        )}
        <ContentWrapper>
          {isSearchLoading && (
            <Spin
              indicator={
                <LoadingOutlined
                  style={{ fontSize: 32, color: '#eab6a8' }}
                  spin
                />
              }
            />
          )}
          {searchError && (
            <SectionWrapper>
              <NormalText16>
                {intl.formatMessage({
                  id: `search.error.${searchError}`,
                })}
              </NormalText16>
            </SectionWrapper>
          )}
          {selectedPaymentGroup && <KioskContent />}
        </ContentWrapper>
      </PageContentWrapper>
    </Layout>
  );
};
