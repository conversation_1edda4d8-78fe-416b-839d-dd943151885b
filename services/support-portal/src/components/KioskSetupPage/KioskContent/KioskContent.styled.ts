import { Collapse } from 'antd';
import styled from 'styled-components';

export const SectionWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  width: 100%;
`;

export const StyledCollapse = styled(Collapse)`
  width: 100%;
  border: none;
  background: transparent;
  margin-top: 32px;
  .ant-collapse-item {
    padding: 16px 0;
    border-radius: 0;
    border-bottom: 1px solid #000000;
  }
  .ant-collapse-item:last-child {
    border-radius: 0;
    border-bottom: 1px solid #000000;
  }
  .ant-collapse-content {
    border-top: none;
    background: transparent;
    padding: 0 24px;
  }
`;
