import { getJwt } from 'utils/jwtStorage';

const API_PATH = '/link/api';

const headers = (jwt: string | null) => ({
  'Content-Type': 'application/json',
  ...(jwt ? { 'X-Auth': jwt } : {}),
});

export const getKioskPackages = async () => {
  const response = await fetch(`${API_PATH}/v1/packages`, {
    headers: headers(await getJwt()),
  });
  if (!response.ok) {
    throw new Error(
      `Failed to get KioskPackages, status code ${response.status}`
    );
  }
  return response.json();
};
