import { useGetKioskPackages } from 'hooks/useQueries';
import { useState } from 'react';
import { KioskPackageType } from 'types/kiosk';
import { PayGroupType } from 'types/locationGroups';
import { createContainer } from 'unstated-next';

export enum SearchError {
  InvalidUuid = 'invalidUuid',
  NotFound = 'notFound',
  NetworkError = 'networkError',
}

const useKioskSetupContainer = (): {
  searchError: SearchError | null;
  setSearchError: (searchError: SearchError | null) => void;
  selectedPaymentGroup: PayGroupType | null;
  setSelectedPaymentGroup: (selectedPaymentGroup: PayGroupType | null) => void;
  resetGroupSelection: () => void;
  isSearchLoading: boolean;
  setIsSearchLoading: (isSearchLoading: boolean) => void;
  kioskPackages: KioskPackageType[];
} => {
  const [isSearchLoading, setIsSearchLoading] = useState(false);
  const [searchError, setSearchError] = useState<SearchError | null>(null);
  const [selectedPaymentGroup, setSelectedPaymentGroup] =
    useState<PayGroupType | null>(null);

  const resetGroupSelection = () => {
    setIsSearchLoading(false);
    setSelectedPaymentGroup(null);
    setSearchError(null);
  };

  const { data: kioskPackages } = useGetKioskPackages();

  return {
    searchError,
    setSearchError,
    selectedPaymentGroup,
    setSelectedPaymentGroup,
    resetGroupSelection,
    isSearchLoading,
    setIsSearchLoading,
    kioskPackages,
  };
};

export const KioskSetupContainer = createContainer(useKioskSetupContainer);
