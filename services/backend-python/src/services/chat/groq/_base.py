import json
import logging
import time
from abc import ABC
from typing import Dict, List, Tuple, Type, TypeVar

from groq._types import NotGivenOr
from pydantic import BaseModel

from companion_app.app.models.chat import ChatHistory
from concierge_app.prompts.response_language_refinement import translate_message_according_to_users_language
from concierge_app.prompts.response_validation import RESPONSE_VALIDATION_SYSTEM_INSTRUCTION
from logger import CustomLoggerHolder
from service_utils.ai.groq import GroqClient
from service_utils.ai.utils.ai_instructions_utils import convert_messages_into_ai_instructions
from service_utils.ai.utils.exceptions import AiException
from service_utils.ai.utils.token_usage import AIMetaData
from services.chat import ChatBot, ChatValidator
from services.chat.models import BaseChatValidationModel, BaseLanguageRefinerModel

StructuredOutputType = TypeVar('StructuredOutputType', bound=BaseModel)
_logger = CustomLoggerHolder.get_instance()


class BaseGroqBot(ABC):
    def __init__(
        self,
        model_name: str = 'meta-llama/llama-4-maverick-17b-128e-instruct',
        *,
        max_tokens: int = 2048,
        top_p: float = 0.9,
        temperature: float = 0.7,
    ):
        """
        For more information about pricing, visit:
        https://groq.com/pricing/

        Compatible model for structured putput/ function calls
        https://console.groq.com/docs/tool-use
        - meta-llama/llama-4-maverick-17b-128e-instruct (BEST ONE)
        - llama-3.3-70b-versatile

        Rate limits:
        https://console.groq.com/docs/rate-limits
        """
        self.model_name = model_name
        self.max_tokens = max_tokens
        self.top_p = top_p
        self.temperature = temperature

    def send_message(
        self,
        messages: List[Dict[str, str]],
        response_format: NotGivenOr[Type[StructuredOutputType]],
        log_context: str,
        **kwargs
    ) -> Tuple[StructuredOutputType, List[AIMetaData]]:
        try:
            # use groq with instructor for structured outputs
            start_time = time.time()
            groq_client = GroqClient.get_instance(use_instructor=True)
            raw_completion = groq_client.chat.completions.create(
                messages=messages,
                model=self.model_name,
                temperature=self.temperature,
                top_p=self.top_p,
                max_completion_tokens=self.max_tokens,
                response_model=response_format
            )
            end_time = time.time()
            response_time = end_time - start_time

        except Exception as e:
            raise RuntimeError(f"Failed to call GroqClient: {e}")

        meta_data = raw_completion._raw_response.usage
        token_usage = AIMetaData(
            ai_model=self.model_name,
            response_time=response_time,
            **meta_data.dict()  # token usage and run time
        )

        _logger.log_with_data(
            logging.DEBUG,
            f"{log_context}",
            raw_completion=raw_completion,
            token_usage=token_usage,
            extra=kwargs,
        )

        if isinstance(raw_completion, response_format):
            return raw_completion, [token_usage]

        try:
            response = self._parse_function_response(response_format, raw_completion)
        except Exception as e:
            raise AiException(f"Failed to parse function response: {e}", token_usage=token_usage)

        return response, [token_usage]

    @staticmethod
    def _parse_function_response(model: type[BaseModel], response):
        message = response.choices[0].message
        if message.function_call:
            # llama-3.3-70b-versatile
            arguments = json.loads(message.function_call.arguments)
            return model(**arguments)
        else:
            raise Exception(f"No function call in response; response={response}")


class GroqAiChatBot(ChatBot, BaseGroqBot):
    def create_chat_response(
        self,
        message: str,
        history: ChatHistory,
        system_message: str,
        structured_output_model: Type[StructuredOutputType],
        **kwargs
    ) -> Tuple[StructuredOutputType, List[AIMetaData]]:
        additional_system_instructions = kwargs.pop("additional_system_instructions", None)

        # Convert messages to AI instructions format
        messages = convert_messages_into_ai_instructions(
            system_message=system_message,
            user_message=message,
            additional_system_instructions=additional_system_instructions,
            message_history=history,
            **kwargs
        )

        # Send the message to the AI service
        response, token_usage = self.send_message(
            messages=messages,
            response_format=structured_output_model,
            log_context="Generate Chat Response by GROQ",
            **kwargs
        )
        return response, token_usage


class GroqAiChatValidator(ChatValidator, BaseGroqBot):
    def validate_chat_response(
        self,
        response: str,
        message: str | None = None,
        system_message: str = RESPONSE_VALIDATION_SYSTEM_INSTRUCTION,
        history: ChatHistory = ChatHistory(),
        **kwargs
    ) -> tuple[BaseChatValidationModel, list[AIMetaData]]:
        messages = convert_messages_into_ai_instructions(
            message_history=history,
            system_message=system_message,
            user_message=message,
            **kwargs,
        )

        # add previous created response to messages
        messages.append({"role": "assistant", "content": response})
        validation_response, token_usage = self.send_message(
            messages=messages,
            response_format=BaseChatValidationModel,
            log_context=f"Validate Chat Response by GROQ {kwargs.get('model')}",
            **kwargs,
        )

        return validation_response, token_usage


class GroqAiChatLanguageRefiner(ChatValidator, BaseGroqBot):
    def __init__(
        self,
        model_name: str = 'meta-llama/llama-4-maverick-17b-128e-instruct',
        *,
        max_tokens: int = 512
    ):
        super().__init__(model_name, max_tokens=max_tokens)

        # Use static values for this language refinement
        self.top_p = 0.95
        self.temperature = 0.7

    def validate_chat_response(
        self,
        response: str,
        system_message: str | None = None,
        message: str | None = None,
        history: ChatHistory = ChatHistory(),
        language: str = 'en_US',
        **kwargs
    ) -> tuple[BaseLanguageRefinerModel, list[AIMetaData]]:
        if system_message is None:
            system_message = translate_message_according_to_users_language(
                language=language,
            )

        messages = convert_messages_into_ai_instructions(system_message=system_message,
                                                         user_message=f'Please translate this message:'
                                                                      f'"""{response}"""'
                                                                      f'Do not give a answer!',
                                                         **kwargs)

        # TODO validation for translation or validate temperature: Sometimes the message (question) is answered
        #  instead of translation.
        translated_response, token_usage = self.send_message(
            messages=messages,
            log_context=f"Translate Chat Response by GROQ {kwargs.get('model')}; language: {language}",
            response_format=BaseLanguageRefinerModel,
            **kwargs,
        )
        _logger.log_with_data(
            logging.DEBUG,
            f"Response after language refinement by GROQ: original: {response}; "
            f"translated: {translated_response.response}",
            original_message=response,
            translated_message=translated_response,
            extra=kwargs,
        )

        return translated_response, token_usage
