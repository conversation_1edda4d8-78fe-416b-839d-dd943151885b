from typing import Dict, List, Optional

from companion_app.app.models.chat import <PERSON><PERSON><PERSON><PERSON><PERSON>
from logger import CustomLoggerHolder

_logger = CustomLoggerHolder.get_instance()


def convert_messages_into_ai_instructions(message_history: Optional[ChatHistory] = None,
                                          system_message: Optional[str] = None,
                                          user_message: Optional[str] = None,
                                          additional_system_instructions: Optional[str] = None,
                                          **kwargs) -> List[Dict[str, str]]:
    instructions = []
    if system_message:
        # do not set language specifics here
        instructions.append({
            "role": "system",
            "content": system_message
        })

    if message_history and message_history.items:
        for message in message_history.items:
            if message.role and message.content.strip():
                instructions.append({"role": message.role, "content": message.content})
            else:
                _logger.warning(
                    f"Skipped adding history message as 'role' or 'content' is not defined in message history: "
                    f"{message_history}", extra=kwargs)

    if user_message:
        # add new message
        instructions.append({"role": "user", "content": user_message})

    if additional_system_instructions:
        # additional system instructions to put in the end of the history -> to initiate a new instruction
        instructions.append({"role": "system", "content": additional_system_instructions})

    return instructions
