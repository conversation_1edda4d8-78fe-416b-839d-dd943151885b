import os
import threading

import instructor
from dotenv import load_dotenv
from groq import Groq


class GroqClient:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(GroqClient, cls).__new__(cls)
                    cls._instance._client = None
        return cls._instance

    @classmethod
    def get_instance(cls, use_instructor=False):
        instance = cls.__new__(cls)  # Ensure the singleton instance is created

        if instance._client is None:  # Lazy initialization
            with cls._lock:
                if instance._client is None:  # Double check inside the lock
                    load_dotenv()
                    if use_instructor:
                        # use instructor for structured outputs
                        instance._client = instructor.from_groq(
                            Groq(api_key=os.getenv('GROQ_API_KEY')),
                            mode=instructor.Mode.JSON)
                    else:
                        instance._client = Groq(api_key=os.getenv('GROQ_API_KEY'))
        return instance._client
