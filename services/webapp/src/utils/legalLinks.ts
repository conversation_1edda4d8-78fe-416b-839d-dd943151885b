import {
  IMP<PERSON><PERSON>,
  IMPRINT_EN,
  PRIVACY_LINK,
  PRIVACY_LINK_EN,
  TERMS_CONDITIONS_LINK,
  TERMS_CONDITIONS_LINK_EN,
  ACCESSIBILITY_LINK,
  ACCESSIBILITY_LINK_EN,
} from 'constants/links';

export const getLucaLegalLinks = (locale: string) => {
  const isGerman = locale === 'de';
  return [
    { localeId: 'generic.imprint', link: isGerman ? IMPRINT : IMPRINT_EN },
    {
      localeId: 'generic.dataPrivacy',
      link: isGerman ? PRIVACY_LINK : PRIVACY_LINK_EN,
    },
    {
      localeId: 'generic.termAndConditions',
      link: isGerman ? TERMS_CONDITIONS_LINK : TERMS_CONDITIONS_LINK_EN,
    },
    {
      localeId: 'generic.accessibility',
      link: isGerman ? ACCESSIBILITY_LINK : ACCESSIBILITY_LINK_EN,
    },
  ];
};
