import { useIntl } from 'react-intl';
import { NormalText15 } from 'components/general';
import { SelfCheckoutContainer } from 'containers';
import { CustomLogo } from '../CustomLogo';
import { AbsoluteContainer, RowWrapper, StyledLink } from './PageFooter.styled';

export const PageFooter = ({
  onLegalInformation,
}: {
  onLegalInformation?: () => void;
}) => {
  const intl = useIntl();
  const { textColor } = SelfCheckoutContainer.useContainer();
  return (
    <AbsoluteContainer>
      <RowWrapper>
        <NormalText15 style={{ color: textColor, fontSize: 21 }}>
          {intl.formatMessage({ id: 'generic.poweredBy' })}
        </NormalText15>
        <NormalText15 style={{ color: textColor, fontSize: 36 }}>
          luca
        </NormalText15>
      </RowWrapper>
      {!!onLegalInformation && <CustomLogo plainLogo />}
      {!!onLegalInformation && (
        <StyledLink style={{ color: textColor }} onClick={onLegalInformation}>
          <NormalText15 style={{ color: textColor, fontSize: 21 }}>
            {intl.formatMessage({ id: 'generic.legalInfo' })}
          </NormalText15>
        </StyledLink>
      )}
    </AbsoluteContainer>
  );
};
