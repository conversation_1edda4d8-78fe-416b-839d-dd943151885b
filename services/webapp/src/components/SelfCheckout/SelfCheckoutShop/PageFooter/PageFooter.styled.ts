import { styled } from 'styled-components';

export const AbsoluteContainer = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 0;
`;

export const RowWrapper = styled.div`
  display: flex;
  align-items: baseline;
  gap: 4px;
`;

export const StyledLink = styled.a`
  margin-right: 32px;
  border: none;
  outline: none;
  cursor: pointer;
`;
