import { useIntl } from 'react-intl';
import { Card } from 'antd';
import { LucaLogoBlackIcon } from 'assets/icons';
import { BoldText15, NormalText15 } from 'components/general';
import { SelfCheckoutContainer } from 'containers';
import { getLucaLegalLinks } from 'utils';
import { LegalLinkItem } from './LegalLinkItem';
import {
  ListWrapper,
  LogoWrapper,
  SectionWrapper,
} from './LegalInformation.styled';
import { HeaderBar } from './HeaderBar';
import { Content } from './LegalInformation.styled';

export const LegalInformation = () => {
  const intl = useIntl();
  const {
    setShowLegalInformation,
    textColor,
    logoUrl,
    legalLinks,
    locationName,
    tileBackgroundColor,
  } = SelfCheckoutContainer.useContainer();

  const lucaLegalLinks = getLucaLegalLinks(intl.locale);

  return (
    <>
      <HeaderBar onBack={() => setShowLegalInformation(false)} />
      <Content>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
          <BoldText15
            style={{ color: textColor, fontSize: 32, lineHeight: '28px' }}
          >
            {intl.formatMessage({ id: 'generic.legalInfo' })}
          </BoldText15>

          {locationName && (
            <NormalText15
              style={{
                color: textColor,
                fontSize: 21,
                lineHeight: '36px',
              }}
            >
              {intl.formatMessage(
                { id: 'stay.legalInfo.description' },
                {
                  locationGroupName: locationName,
                }
              )}
            </NormalText15>
          )}

          {legalLinks && (
            <Card
              style={{
                width: '100%',
                boxShadow: `2px 2px 4px 0px ${textColor}26`,
                backgroundColor: tileBackgroundColor,
                border: 0,
              }}
              bodyStyle={{ padding: 16, display: 'flex' }}
            >
              <SectionWrapper>
                <LogoWrapper>
                  <img
                    style={{ height: '50px' }}
                    alt="logo"
                    src={logoUrl?.light}
                  />
                </LogoWrapper>
                <ListWrapper>
                  {legalLinks.map(
                    linkItem =>
                      linkItem.localeId &&
                      linkItem.link && (
                        <LegalLinkItem
                          localeId={linkItem.localeId}
                          link={linkItem.link}
                          key={linkItem.localeId}
                        />
                      )
                  )}
                </ListWrapper>
              </SectionWrapper>
            </Card>
          )}

          <Card
            style={{
              width: '100%',
              boxShadow: `2px 2px 4px 0px ${textColor}26`,
              backgroundColor: tileBackgroundColor,
              border: 0,
            }}
            bodyStyle={{
              display: 'flex',
              marginTop: '16px',
              flexDirection: 'column',
            }}
          >
            <LucaLogoBlackIcon height="50px" style={{ color: textColor }} />
            <ListWrapper>
              {lucaLegalLinks.map(linkItem => (
                <LegalLinkItem
                  localeId={linkItem.localeId}
                  link={linkItem.link}
                  key={linkItem.localeId}
                />
              ))}
            </ListWrapper>
          </Card>
        </div>
      </Content>
    </>
  );
};
