import { useIntl } from 'react-intl';
import { useState } from 'react';
import { BoldText15, StayModal, TextButton } from 'components/general';
import { SelfCheckoutContainer } from 'containers';
import {
  Container,
  ModalHeader,
} from 'components/LucaStayCheckinTerminal/shared';
import { StyledLink } from './LegalLinkItem.styled';

type LegalLinkItemProperties = {
  localeId: string;
  link: string;
};
export const LegalLinkItem = ({ localeId, link }: LegalLinkItemProperties) => {
  const intl = useIntl();
  const { textColor, tileBackgroundColor, primaryColor } =
    SelfCheckoutContainer.useContainer();

  const [url, setUrl] = useState<string | null>(null);

  return (
    <>
      <StyledLink
        onClick={() => setUrl(link)}
        rel="noopener noreferrer"
        target="_blank"
      >
        <BoldText15 style={{ fontSize: 21, color: textColor }}>
          {intl.formatMessage({
            id: localeId,
          })}
        </BoldText15>
      </StyledLink>
      {url && (
        <StayModal open closable={false} backgroundColor={tileBackgroundColor}>
          <Container>
            <ModalHeader>
              <TextButton
                style={{ color: primaryColor }}
                onClick={() => setUrl(null)}
                data-cy="closeButton"
              >
                {intl.formatMessage({ id: 'generic.cancel' })}
              </TextButton>
            </ModalHeader>

            <iframe title={link} src={url} width="100%" height="1000px" />
          </Container>
        </StayModal>
      )}
    </>
  );
};
