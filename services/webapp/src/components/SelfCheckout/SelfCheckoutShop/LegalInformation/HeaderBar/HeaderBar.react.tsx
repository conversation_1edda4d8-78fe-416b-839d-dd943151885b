import { useIntl } from 'react-intl';
import { CloseOutlined, LeftOutlined } from '@ant-design/icons';
import { SelfCheckoutContainer } from 'containers';
import { TextButton } from 'components/general';
import { ButtonWrapper, Wrapper } from './HeaderBar.styled';

export const HeaderBar = ({ onBack }: { onBack?: () => void }) => {
  const intl = useIntl();
  const { textColor, setShowLegalInformation } =
    SelfCheckoutContainer.useContainer();

  return (
    <Wrapper>
      {onBack ? (
        <ButtonWrapper onClick={onBack} data-cy="backButton">
          <LeftOutlined style={{ color: textColor, fontSize: 21 }} />
          <TextButton $color={textColor}>
            {intl.formatMessage({ id: 'generic.back' })}
          </TextButton>
        </ButtonWrapper>
      ) : (
        <div />
      )}
      <ButtonWrapper onClick={() => setShowLegalInformation(false)}>
        <CloseOutlined style={{ color: textColor, fontSize: 21 }} />
        <TextButton $color={textColor}>
          {intl.formatMessage({ id: 'generic.cancel' })}
        </TextButton>
      </ButtonWrapper>
    </Wrapper>
  );
};
