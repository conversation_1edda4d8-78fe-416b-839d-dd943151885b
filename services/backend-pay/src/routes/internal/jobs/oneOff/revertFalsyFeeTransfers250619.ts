import {
  AdditionalTransfers,
  JobCompletion,
  V3Payment,
  database,
} from 'database';
import { Request, Response } from 'express';
import { Op } from 'sequelize';
import { performance } from 'perf_hooks';
import logger from 'utils/logger';
import { AdditionalTransferType } from 'database/models/additionalTransfer';
import { PaymentProviderCode } from 'routes/v3/paymentProvider/types';
import { AdditionalTransferStatus } from 'constants/additionalTransfers';
import { transferFromMasterToLocationGroup } from 'utils/payments/intraAccountTransfers';

const JOB_NAME = 'revertFalsyFeeTransfers250619';

// These fees have been wrongly collected
const transfersToRevert = [
  'c5be92b9-7ba5-4ba2-b54c-d06d57dbc5cd',
  '57c2e257-cc73-402d-b48f-fe4e0a43b187', // tax collection
  '8aa5fbf4-2047-4c93-ae44-70cafdc47c78',
  'c22c9fb4-3b9a-4296-bde5-6b7747caa10e', // tax collection
  '1443c6e4-835d-41b1-89de-c180da48dc6e',
  'cd4b42a7-399e-482e-9d30-5265453b99cb', // tax collection
];

export const revertFalsyFeeTransfers250619 = async (
  request: Request,
  response: Response
): Promise<void> => {
  const t0 = performance.now();
  const startedAt = new Date();

  logger.warn(`[${JOB_NAME}] Starting job execution`);

  try {
    const additionalTransfers = await AdditionalTransfers.findAll({
      where: {
        uuid: transfersToRevert,
        payoutType: [
          AdditionalTransferType.fees,
          AdditionalTransferType.feeTaxes,
        ],
        paymentProviderCode: PaymentProviderCode.ADYEN,
        name: {
          [Op.notLike]: 'Revert%',
        },
        reversedAdditionalTransferId: null,
      },
    });

    logger.warn(
      `[${JOB_NAME}] Found ${additionalTransfers.length} additional transfers to process`
    );

    for (const transfer of additionalTransfers) {
      if (transfer.transferId) {
        const negativeAmount = transfer.amount.multiply(-1);
        const newTransfer = await AdditionalTransfers.create({
          name: `Revert - ${transfer.uuid} - ${transfer.name}`,
          amount: negativeAmount,
          payoutType: transfer.payoutType,
          locationGroupId: transfer.locationGroupId,
          paymentProviderCode: transfer.paymentProviderCode,
          status: AdditionalTransferStatus.STARTED,
        });

        const { transferId } = await transferFromMasterToLocationGroup({
          paymentProviderCode: PaymentProviderCode.ADYEN,
          destinationLocationGroupId: transfer.locationGroupId,
          amount: transfer.amount,
          additionalTransferId1: transfer.uuid,
        });
        if (transferId) {
          await database.transaction(async transaction => {
            await newTransfer.update({ transferId }, { transaction });
            await transfer.update(
              {
                reversedAdditionalTransferId: newTransfer.uuid,
              },
              { transaction }
            );
          });
        } else {
          logger.error(
            `[${JOB_NAME}] no transferId. Transfer ${transfer.uuid} error`
          );
        }
      } else {
        await transfer.destroy();
      }

      await V3Payment.update(
        {
          feeCollectionAdditionalTransferId: null,
          feeTaxCollectionAdditionalTransferId: null,
        },
        {
          where: {
            [Op.or]: [
              { feeCollectionAdditionalTransferId: transfer.uuid },
              { feeTaxCollectionAdditionalTransferId: transfer.uuid },
            ],
          },
        }
      );
    }

    const responseData = {
      affectedRows: additionalTransfers.length,
      time: performance.now() - t0,
    };
    const jobCompletion = await JobCompletion.create({
      name: JOB_NAME,
      meta: responseData,
      startedAt,
    });
    logger.warn(`[${JOB_NAME}] Job completed successfully`, jobCompletion);

    response.send(responseData);
  } catch (error) {
    const errorDetails =
      error instanceof Error
        ? `Message: ${error.message}, Stack: ${error.stack}`
        : 'An unknown error occurred';
    logger.error(`[${JOB_NAME}] Job execution failed`, { error: errorDetails });
    const responseData = {
      status: 'FAILED',
      error: errorDetails,
      time: (performance.now() - t0).toString(),
    };
    await JobCompletion.create({
      name: JOB_NAME,
      meta: responseData,
      startedAt,
    });
    response.status(500).send(responseData);
  }
};
