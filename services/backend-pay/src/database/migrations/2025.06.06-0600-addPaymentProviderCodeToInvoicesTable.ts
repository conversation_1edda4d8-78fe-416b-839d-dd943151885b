import { DataTypes } from 'sequelize';
import { Migration } from 'types/umzug';

const migration: Migration = {
  up: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.transaction(async transaction => {
      // Add the paymentProviderCode column as nullable first
      await queryInterface.addColumn(
        'Invoices',
        'paymentProviderCode',
        {
          type: DataTypes.STRING,
          allowNull: true,
          references: {
            model: 'PaymentProviders',
            key: 'code',
          },
        },
        { transaction }
      );

      // First, log any LocationGroups that have both Adyen and Rapyd enabled
      const [conflictingGroups] = await queryInterface.sequelize.query(
        `
        SELECT
          lg.uuid,
          lg."companyName",
          COUNT(CASE WHEN lgppa."paymentProviderCode" = 'adyen' AND lgppa."isEnabled" = true THEN 1 END) as adyen_enabled,
          COUNT(CASE WHEN lgppa."paymentProviderCode" = 'rapyd' AND lgppa."isEnabled" = true THEN 1 END) as rapyd_enabled
        FROM "LocationGroups" lg
        LEFT JOIN "LocationGroupPaymentProviderAssociations" lgppa ON lg.uuid = lgppa."locationGroupId"
        WHERE lgppa."isEnabled" = true
        GROUP BY lg.uuid, lg."companyName"
        HAVING COUNT(CASE WHEN lgppa."paymentProviderCode" = 'adyen' AND lgppa."isEnabled" = true THEN 1 END) > 0
           AND COUNT(CASE WHEN lgppa."paymentProviderCode" = 'rapyd' AND lgppa."isEnabled" = true THEN 1 END) > 0;
      `,
        { transaction }
      );

      if (conflictingGroups.length > 0) {
        console.log(
          `Warning: Found ${conflictingGroups.length} LocationGroups with both Adyen and Rapyd enabled:`
        );
        (conflictingGroups as Record<string, string | number>[]).forEach(
          group => {
            console.log(
              `- ${group.companyName} (${group.uuid}): Adyen enabled: ${
                (group.adyen_enabled as number) > 0
              }, Rapyd enabled: ${(group.rapyd_enabled as number) > 0}`
            );
          }
        );
        console.log(
          'These will be assigned to Adyen by default (first enabled provider found).'
        );
      }

      // Update invoices based on LocationGroup's enabled payment provider associations
      // Priority: Adyen if enabled, then Rapyd if enabled, default to 'rapyd' if no associations found
      const updateQuery = `
        UPDATE "Invoices"
        SET "paymentProviderCode" = COALESCE(
          (
            SELECT lgppa."paymentProviderCode"
            FROM "LocationGroupPaymentProviderAssociations" lgppa
            WHERE lgppa."locationGroupId" = "Invoices"."locationGroupId"
              AND lgppa."isEnabled" = true
            ORDER BY
              CASE
                WHEN lgppa."paymentProviderCode" = 'adyen' THEN 1
                WHEN lgppa."paymentProviderCode" = 'rapyd' THEN 2
                ELSE 3
              END
            LIMIT 1
          ),
          'rapyd'
        )
        WHERE "Invoices"."paymentProviderCode" IS NULL;
      `;

      await queryInterface.sequelize.query(updateQuery, { transaction });

      // Make the column NOT NULL after populating existing data
      await queryInterface.changeColumn(
        'Invoices',
        'paymentProviderCode',
        {
          type: DataTypes.STRING,
          allowNull: false,
          references: {
            model: 'PaymentProviders',
            key: 'code',
          },
        },
        { transaction }
      );

      // Add an index for better query performance
      await queryInterface.addIndex('Invoices', {
        fields: ['paymentProviderCode'],
        name: 'Invoices_paymentProviderCode',
        transaction,
      });
    });
  },

  down: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.transaction(async transaction => {
      await queryInterface.removeIndex(
        'Invoices',
        'Invoices_paymentProviderCode',
        { transaction }
      );

      await queryInterface.removeColumn('Invoices', 'paymentProviderCode', {
        transaction,
      });
    });
  },
};

export default migration;
