import { Migration } from 'types/umzug';
import { DataTypes } from 'sequelize';
import { SegmentSmsTemplate } from 'database';
import { WhatsAppTemplateType } from 'utils/stay/stayWhatsappTemplate';
import { v4 } from 'uuid';

const migration: Migration = {
  up: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.transaction(async transaction => {
      await queryInterface.sequelize.query(
        `DELETE FROM "SegmentSmsTemplates"
         WHERE "locationId" IS NOT NULL
         OR "locationGroupId" IS NOT NULL`,
        { transaction }
      );

      const currentTable = await queryInterface.describeTable(
        'SegmentSmsTemplates'
      );

      if (!currentTable.type) {
        await queryInterface.addColumn(
          'SegmentSmsTemplates',
          'type',
          {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: null,
          },
          { transaction }
        );
      }

      const existingTemplate = await SegmentSmsTemplate.findOne({
        where: {
          templateName: 'Hotel Pre-Checkin Reminder',
          locationId: null,
          locationGroupId: null,
        },
        transaction,
      });

      if (existingTemplate) {
        const templateData = existingTemplate.toJSON();
        await queryInterface.bulkInsert(
          'SegmentSmsTemplates',
          [
            {
              ...templateData,
              uuid: v4(),
              type:
                WhatsAppTemplateType.SEND_HOTEL_PRE_CHECKIN_REMINDER_WHATSAPP,
              sendTimeEvent: null,
              sendTimeOffset: 0,
              isActive: false,
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          ],
          { transaction }
        );
      }

      await queryInterface.bulkUpdate(
        'SegmentSmsTemplates',
        {
          type: WhatsAppTemplateType.SEND_HOTEL_CHECKOUT_REMINDER_WHATSAPP,
          sendTimeEvent: null,
          sendTimeOffset: 0,
          isActive: false,
        },
        { templateName: 'sendHotelCheckoutReminderWhatsApp' },
        { transaction }
      );
    });
  },

  down: async ({ context: queryInterface }) => {
    await queryInterface.sequelize.transaction(async _transaction => {});
  },
};

export default migration;
