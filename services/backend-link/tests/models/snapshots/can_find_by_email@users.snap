---
source: tests/models/users.rs
expression: existing_user
---
Ok(
    Model {
        created_at: 2023-11-12T12:34:56.789+00:00,
        updated_at: 2023-11-12T12:34:56.789+00:00,
        id: 1,
        pid: 11111111-1111-1111-1111-111111111111,
        email: "<EMAIL>",
        password: "$argon2id$v=19$m=19456,t=2,p=1$ETQBx4rTgNAZhSaeYZKOZg$eYTdH26CRT6nUJtacLDEboP0li6xUwUF/q5nSlQ8uuc",
        api_key: "lo-95ec80d7-cb60-4b70-9b4b-9ef74cb88758",
        name: "user1",
        reset_token: None,
        reset_sent_at: None,
        email_verification_token: None,
        email_verification_sent_at: None,
        email_verified_at: None,
        magic_link_token: None,
        magic_link_expiration: None,
    },
)
